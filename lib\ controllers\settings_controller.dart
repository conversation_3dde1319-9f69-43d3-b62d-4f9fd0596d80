import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsController extends GetxController {
  // Observable variables
  var sleepTimerText = 'إيقاف'.obs;
  var totalPlayTime = '32س 29د'.obs;
  var hiddenFilesCount = 12.obs;
  var deletedFilesCount = 14.obs;
  var currentLanguage = 'العربية'.obs;
  var playbackNotifications = true.obs;
  var systemNotifications = true.obs;
  var songsPlayed = 245.obs;
  var videosWatched = 89.obs;
  var dailyAverage = '2س 15د'.obs;

  @override
  void onInit() {
    super.onInit();
    loadSettings();
  }

  void loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    sleepTimerText.value = prefs.getString('sleep_timer') ?? 'إيقاف';
    currentLanguage.value = prefs.getString('language') ?? 'العربية';
    playbackNotifications.value = prefs.getBool('playback_notifications') ?? true;
    systemNotifications.value = prefs.getBool('system_notifications') ?? true;
  }

  void setSleepTimer(String timer) async {
    sleepTimerText.value = timer;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('sleep_timer', timer);
    
    if (timer != 'إيقاف') {
      Get.snackbar('مؤقت النوم', 'تم تعيين المؤقت على $timer');
    }
  }

  void setLanguage(String language) async {
    currentLanguage.value = language;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('language', language);
    Get.snackbar('اللغة', 'تم تغيير اللغة إلى $language');
  }

  void togglePlaybackNotifications() async {
    playbackNotifications.value = !playbackNotifications.value;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('playback_notifications', playbackNotifications.value);
  }

  void toggleSystemNotifications() async {
    systemNotifications.value = !systemNotifications.value;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('system_notifications', systemNotifications.value);
  }

  void updatePlayTime(Duration duration) {
    // Update total play time
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    totalPlayTime.value = '${hours}س ${minutes}د';
  }

  void incrementSongsPlayed() {
    songsPlayed.value++;
  }

  void incrementVideosWatched() {
    videosWatched.value++;
  }
}