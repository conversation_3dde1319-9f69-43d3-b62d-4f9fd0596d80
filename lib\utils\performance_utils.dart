import 'package:flutter/material.dart';
import 'package:lazy_load_scrollview/lazy_load_scrollview.dart';

class PerformanceUtils {
  // Lazy loading for large lists
  static Widget buildLazyList<T>({
    required List<T> items,
    required Widget Function(T item, int index) itemBuilder,
    int itemsPerPage = 20,
  }) {
    return LazyLoadScrollView(
      onEndOfPage: () => _loadMoreItems(),
      child: ListView.builder(
        itemCount: items.length,
        itemBuilder: (context, index) => itemBuilder(items[index], index),
      ),
    );
  }

  static void _loadMoreItems() {
    // Implement pagination logic
  }
}
