import 'package:flutter/material.dart';
import 'dart:io';

class LyricsPage extends StatelessWidget {
  final dynamic song;

  const LyricsPage({super.key, required this.song});

  String? get lyrics {
    if (song is File) {
      return null; // Files don't have lyrics
    }
    if (song is Map) {
      return song['lyrics'];
    }
    return song?.lyrics;
  }

  String get title {
    if (song is File) {
      return (song as File).path.split('/').last.split('.').first;
    }
    if (song is Map) {
      return song['title'] ?? 'أغنية غير معروفة';
    }
    return song?.title ?? 'أغنية غير معروفة';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("كلمات: $title")),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text(
          lyrics ?? "لا توجد كلمات متاحة لهذه الأغنية.",
          style: const TextStyle(fontSize: 16),
        ),
      ),
    );
  }
}
