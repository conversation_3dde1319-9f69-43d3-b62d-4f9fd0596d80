import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../ controllers/media_controller.dart';

class FoldersPage extends StatelessWidget {
  const FoldersPage({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('المجلدات'),
          bottom: const TabBar(
            tabs: [
              Tab(text: 'فيديو'),
              Tab(text: 'صوت'),
            ],
          ),
        ),
        body: const TabBarView(
          children: [
            VideoFoldersTab(),
            AudioFoldersTab(),
          ],
        ),
      ),
    );
  }
}

class VideoFoldersTab extends StatelessWidget {
  const VideoFoldersTab({super.key});

  @override
  Widget build(BuildContext context) {
    final mediaController = Get.find<MediaController>();

    return Obx(() {
      if (mediaController.videoFolders.isEmpty) {
        return const Center(child: Text('لا توجد مجلدات فيديو'));
      }
      return ListView.builder(
        itemCount: mediaController.videoFolders.length,
        itemBuilder: (ctx, index) {
          final folder = mediaController.videoFolders[index];
          return Card(
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: ListTile(
              leading: const Icon(Icons.folder),
              title: Text(folder.path.split('/').last),
              subtitle: Text(folder.path),
              onTap: () {
                // افتح المجلد وعرض محتوياته
              },
            ),
          );
        },
      );
    });
  }
}

class AudioFoldersTab extends StatelessWidget {
  const AudioFoldersTab({super.key});

  @override
  Widget build(BuildContext context) {
    final mediaController = Get.find<MediaController>();

    return Obx(() {
      if (mediaController.audioFolders.isEmpty) {
        return const Center(child: Text('لا توجد مجلدات صوت'));
      }
      return ListView.builder(
        itemCount: mediaController.audioFolders.length,
        itemBuilder: (ctx, index) {
          final folder = mediaController.audioFolders[index];
          return Card(
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: ListTile(
              leading: const Icon(Icons.folder),
              title: Text(folder.path.split('/').last),
              subtitle: Text(folder.path),
              onTap: () {
                // افتح المجلد وعرض محتوياته
              },
            ),
          );
        },
      );
    });
  }
}