import 'package:flutter/material.dart';
import 'package:get/get.dart';
// import '../controllers/playlist_controller.dart';
import 'audio_home_page.dart';
import 'video/video_home_page.dart';
import 'playlist_page.dart';
import 'folders/folders_page.dart';
import 'settings/settings_page.dart';

class MainNavigationPage extends StatefulWidget {
  const MainNavigationPage({super.key});

  @override
  State<MainNavigationPage> createState() => _MainNavigationPageState();
}

class _MainNavigationPageState extends State<MainNavigationPage> {
  int _currentIndex = 0;
  final PageController _pageController = PageController();

  final List<Widget> _pages = [
    const AudioHomePage(),
    const VideoHomePage(),
    const PlaylistPage(),
    const FoldersPage(),
    const SettingsPage(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        children: _pages,
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Get.theme.colorScheme.primary,
              Get.theme.colorScheme.secondary
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
            _pageController.animateToPage(
              index,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          },
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.transparent,
          elevation: 0,
          selectedItemColor: Get.theme.colorScheme.onPrimary,
          unselectedItemColor:
              Get.theme.colorScheme.onPrimary.withValues(alpha: 0.7),
          selectedLabelStyle: const TextStyle(fontWeight: FontWeight.bold),
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.music_note),
              label: 'الصوت',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.video_library),
              label: 'الفيديو',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.playlist_play),
              label: 'القوائم',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.folder),
              label: 'المجلدات',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.settings),
              label: 'الإعدادات',
            ),
          ],
        ),
      ),
    );
  }
}
