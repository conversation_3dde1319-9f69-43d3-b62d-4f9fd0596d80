import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ErrorHandler {
  static void handleError(dynamic error, {String? context}) {
    // Log error for debugging
    print('Error in $context: $error');
    
    // Show user-friendly message
    Get.snackbar(
      'خطأ',
      _getErrorMessage(error),
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red.shade100,
      colorText: Colors.red.shade800,
      duration: const Duration(seconds: 3),
    );
  }
  
  static String _getErrorMessage(dynamic error) {
    if (error.toString().contains('permission')) {
      return 'يرجى منح الصلاحيات المطلوبة';
    }
    return 'حدث خطأ غير متوقع';
  }
}