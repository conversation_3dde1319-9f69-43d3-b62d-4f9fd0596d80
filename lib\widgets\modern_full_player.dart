import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:on_audio_query_forked/on_audio_query.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';
import 'package:share_plus/share_plus.dart';

import '../ controllers/audio_controller.dart';
import '../pages/lyrics_page.dart';

class FullPlayer extends StatelessWidget {
  final PanelController panelController;

  FullPlayer({super.key, required this.panelController});

  @override
  Widget build(BuildContext context) {
    final AudioController controller = Get.find<AudioController>();

    return Container(
      decoration: const BoxDecoration(
        color: Colors.black,
      ),
      child: Obx(() {
        if (!controller.hasCurrentTrack) {
          return const Center(
            child: Text(
              'لا يوجد ملف صوتي',
              style: TextStyle(color: Colors.white70, fontSize: 18),
            ),
          );
        }

        return SafeArea(
          child: Column(
            children: [
              // Handle Bar
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 20),

              // Header with back button and title
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back_ios,
                          color: Colors.white, size: 20),
                      onPressed: () {
                        if (panelController.isAttached) {
                          panelController.close();
                        }
                      },
                    ),
                    Expanded(
                      child: Text(
                        controller.currentTitle,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.refresh,
                          color: Colors.white, size: 20),
                      onPressed: () => controller.replayCurrent(),
                    ),
                    IconButton(
                      icon: const Icon(Icons.more_vert,
                          color: Colors.white, size: 20),
                      onPressed: () => _showMoreOptions(context, controller),
                    ),
                  ],
                ),
              ),

              const Spacer(),

              // Video thumbnail area (based on the design)
              Container(
                width: double.infinity,
                height: 200,
                margin: const EdgeInsets.symmetric(horizontal: 20),
                decoration: BoxDecoration(
                  color: Colors.grey.shade800,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Stack(
                  children: [
                    // Video thumbnail placeholder
                    Container(
                      width: double.infinity,
                      height: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: LinearGradient(
                          colors: [
                            Colors.teal.shade700,
                            Colors.teal.shade500,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                    ),
                    // Play controls overlay
                    Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildVideoControlButton(
                            icon: Icons.skip_previous,
                            onPressed: controller.playPrevious,
                          ),
                          _buildVideoControlButton(
                            icon: controller.isPlaying.value
                                ? Icons.pause
                                : Icons.play_arrow,
                            onPressed: controller.togglePlayPause,
                            isMain: true,
                          ),
                          _buildVideoControlButton(
                            icon: Icons.skip_next,
                            onPressed: controller.playNext,
                          ),
                        ],
                      ),
                    ),
                    // Bottom overlay with text
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.only(
                            bottomLeft: Radius.circular(12),
                            bottomRight: Radius.circular(12),
                          ),
                          gradient: LinearGradient(
                            colors: [
                              Colors.transparent,
                              Colors.black.withValues(alpha: 0.7),
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                        ),
                        child: Text(
                          'يُعلم جميع كبار الطيران والشركات الدولية وقد شهدنا الكثير التي كانت تبقى في مطار بن غوريون قبل أسبوعين',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 30),

              // Progress bar
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    SliderTheme(
                      data: SliderTheme.of(context).copyWith(
                        activeTrackColor: Colors.white,
                        inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
                        thumbColor: Colors.white,
                        overlayColor: Colors.white.withValues(alpha: 0.2),
                        trackHeight: 2,
                        thumbShape: const RoundSliderThumbShape(
                          enabledThumbRadius: 6,
                        ),
                      ),
                      child: Slider(
                        value: controller.currentPosition.value.inMilliseconds
                            .toDouble(),
                        max:
                            controller.duration.value.inMilliseconds.toDouble(),
                        onChanged: (value) {
                          controller
                              .seekTo(Duration(milliseconds: value.toInt()));
                        },
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          _formatDuration(controller.currentPosition.value),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          _formatDuration(controller.duration.value),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 30),

              // Bottom control buttons
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 40),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildBottomControlButton(
                      icon: Icons.headphones,
                      onPressed: () {},
                    ),
                    _buildBottomControlButton(
                      icon: Icons.repeat,
                      onPressed: controller.toggleRepeat,
                    ),
                    _buildBottomControlButton(
                      icon: Icons.lock,
                      onPressed: () {},
                    ),
                    _buildBottomControlButton(
                      icon: Icons.menu,
                      onPressed: () => _showMoreOptions(context, controller),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildVideoControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    bool isMain = false,
  }) {
    return Container(
      width: isMain ? 60 : 50,
      height: isMain ? 60 : 50,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white.withValues(alpha: isMain ? 0.9 : 0.7),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(30),
          onTap: onPressed,
          child: Icon(
            icon,
            color: Colors.black,
            size: isMain ? 30 : 24,
          ),
        ),
      ),
    );
  }

  Widget _buildBottomControlButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: 45,
      height: 45,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white.withValues(alpha: 0.1),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(22.5),
          onTap: onPressed,
          child: Icon(
            icon,
            color: Colors.white,
            size: 20,
          ),
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  void _showMoreOptions(BuildContext context, AudioController controller) {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.lyrics),
              title: const Text('عرض الكلمات'),
              onTap: () {
                Get.back();
                if (controller.currentMediaItem.value != null) {
                  Get.to(() =>
                      LyricsPage(song: controller.currentMediaItem.value!));
                }
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('مشاركة'),
              onTap: () {
                Get.back();
                if (controller.currentMediaItem.value != null) {
                  Share.shareXFiles(
                    [XFile(controller.currentMediaItem.value!.path)],
                    text: 'مشاركة ملف صوتي: ${controller.currentTitle}',
                  );
                }
              },
            ),
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('معلومات الملف'),
              onTap: () {
                Get.back();
                _showFileInfo(controller);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showFileInfo(AudioController controller) {
    Get.dialog(
      AlertDialog(
        title: const Text('معلومات الملف'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('العنوان: ${controller.currentTitle}'),
            Text('الفنان: ${controller.currentArtist}'),
            if (controller.currentMediaItem.value != null)
              Text('المسار: ${controller.currentMediaItem.value!.path}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
