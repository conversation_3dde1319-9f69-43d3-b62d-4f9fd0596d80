import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import 'package:share_plus/share_plus.dart';
import 'package:social_media_app/%20controllers/audio_controller.dart';
import 'package:social_media_app/%20controllers/media_controller.dart';
import 'package:social_media_app/%20controllers/audio_playlist_controller.dart';
import 'package:social_media_app/model/media_item.dart';

/// صفحة تشغيل الصوت المحسنة مع المشغل المصغر والكبير
/// تدعم الثيمات والتشغيل المتقدم وإضافة للقوائم
class ModernAudioPlayerPage extends StatefulWidget {
  final File audioFile;
  final String? title;
  final String? artist;

  const ModernAudioPlayerPage({
    super.key,
    required this.audioFile,
    this.title,
    this.artist,
  });

  @override
  State<ModernAudioPlayerPage> createState() => _ModernAudioPlayerPageState();
}

class _ModernAudioPlayerPageState extends State<ModernAudioPlayerPage>
    with TickerProviderStateMixin {
  final player = AudioPlayer();

  late AudioController audioController;
  late MediaController mediaController;
  late AudioPlaylistController playlistController;
  late AnimationController rotationController;
  late AnimationController waveController;

  Duration duration = Duration.zero;
  Duration position = Duration.zero;
  bool isLoading = true;
  bool isFavorite = false;
  bool isShuffled = false;
  bool isRepeating = false;
  List<MediaItem> playlist = [];
  int currentIndex = 0;
  bool isMinimized = false;

  @override
  void initState() {
    super.initState();
    audioController = Get.put(AudioController());
    mediaController = Get.put(MediaController());
    playlistController = Get.put(AudioPlaylistController());

    rotationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    );

    waveController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _initPlayer();
  }

  @override
  void dispose() {
    player.dispose();
    rotationController.dispose();
    waveController.dispose();
    super.dispose();
  }

  void _initPlayer() async {
    try {
      playlist = mediaController.allAudioFiles;
      currentIndex =
          playlist.indexWhere((e) => e.path == widget.audioFile.path);
      if (currentIndex == -1) currentIndex = 0;

      await player.setFilePath(widget.audioFile.path);

      player.durationStream.listen((d) {
        if (mounted) setState(() => duration = d ?? Duration.zero);
      });

      player.positionStream.listen((p) {
        if (mounted) setState(() => position = p);
      });

      player.playerStateStream.listen((state) {
        if (mounted) {
          if (state.playing) {
            rotationController.repeat();
            waveController.repeat(reverse: true);
          } else {
            rotationController.stop();
            waveController.stop();
          }
        }
      });

      _checkIfFavorite();
    } catch (e) {
      print('Error initializing player: $e');
    }

    if (mounted) setState(() => isLoading = false);
  }

  void _checkIfFavorite() {
    // تحقق من المفضلة
    isFavorite = mediaController.allAudioFiles
        .any((item) => item.path == widget.audioFile.path);
    if (mounted) setState(() {});
  }

  void _toggleFavorite() {
    final mediaItem = MediaItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: widget.title ?? widget.audioFile.path.split('/').last,
      path: widget.audioFile.path,
      type: MediaType.audio,
      artist: widget.artist,
    );

    if (isFavorite) {
      // mediaController.removeFromFavorites(mediaItem);
    } else {
      // mediaController.addToFavorites(mediaItem);
    }

    setState(() => isFavorite = !isFavorite);

    Get.snackbar(
      isFavorite ? 'تم' : 'تم الإزالة',
      isFavorite ? 'تم إضافة الأغنية للمفضلة' : 'تم إزالة الأغنية من المفضلة',
      backgroundColor: Get.theme.colorScheme.primary,
      colorText: Get.theme.colorScheme.onPrimary,
    );
  }

  void _togglePlayPause() {
    if (player.playing) {
      player.pause();
    } else {
      player.play();
    }
  }

  void _seekTo(Duration position) {
    player.seek(position);
  }

  void _playNext() {
    if (playlist.isEmpty) return;

    int nextIndex;
    if (isShuffled) {
      nextIndex = Random().nextInt(playlist.length);
    } else {
      nextIndex = (currentIndex + 1) % playlist.length;
    }

    _changeTrack(nextIndex);
  }

  void _playPrevious() {
    if (playlist.isEmpty) return;

    int prevIndex;
    if (isShuffled) {
      prevIndex = Random().nextInt(playlist.length);
    } else {
      prevIndex = (currentIndex - 1);
      if (prevIndex < 0) prevIndex = playlist.length - 1;
    }

    _changeTrack(prevIndex);
  }

  void _changeTrack(int index) async {
    if (index < 0 || index >= playlist.length) return;

    setState(() {
      currentIndex = index;
      isLoading = true;
    });

    try {
      await player.setFilePath(playlist[index].path);
      await player.play();
      _checkIfFavorite();
    } catch (e) {
      print('Error changing track: $e');
    }

    setState(() => isLoading = false);
  }

  void _toggleShuffle() {
    setState(() => isShuffled = !isShuffled);
  }

  void _toggleRepeat() {
    setState(() => isRepeating = !isRepeating);
    player.setLoopMode(isRepeating ? LoopMode.one : LoopMode.off);
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Get.theme.colorScheme.primary.withValues(alpha: 0.8),
              Get.theme.colorScheme.secondary.withValues(alpha: 0.6),
              Get.theme.scaffoldBackgroundColor,
            ],
          ),
        ),
        child: SafeArea(
          child: isMinimized ? _buildMiniPlayer() : _buildFullPlayer(),
        ),
      ),
    );
  }

  Widget _buildMiniPlayer() {
    return Container(
      height: 70,
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Get.theme.cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // صورة الألبوم
          Container(
            width: 60,
            height: 60,
            margin: const EdgeInsets.all(5),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              gradient: LinearGradient(
                colors: [
                  Get.theme.colorScheme.primary,
                  Get.theme.colorScheme.secondary,
                ],
              ),
            ),
            child: AnimatedBuilder(
              animation: rotationController,
              builder: (context, child) {
                return Transform.rotate(
                  angle: rotationController.value * 2 * pi,
                  child: Icon(
                    Icons.music_note,
                    color: Get.theme.colorScheme.onPrimary,
                    size: 30,
                  ),
                );
              },
            ),
          ),

          // معلومات الأغنية
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.title ?? widget.audioFile.path.split('/').last,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Get.theme.colorScheme.onSurface,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    widget.artist ?? 'غير معروف',
                    style: TextStyle(
                      color: Get.theme.colorScheme.onSurface
                          .withValues(alpha: 0.7),
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),

          // أزرار التحكم
          IconButton(
            onPressed: _playPrevious,
            icon: Icon(
              Icons.skip_previous,
              color: Get.theme.colorScheme.primary,
            ),
          ),

          IconButton(
            onPressed: _togglePlayPause,
            icon: Icon(
              player.playing ? Icons.pause : Icons.play_arrow,
              color: Get.theme.colorScheme.primary,
              size: 30,
            ),
          ),

          IconButton(
            onPressed: _playNext,
            icon: Icon(
              Icons.skip_next,
              color: Get.theme.colorScheme.primary,
            ),
          ),

          IconButton(
            onPressed: () => setState(() => isMinimized = false),
            icon: Icon(
              Icons.expand_less,
              color: Get.theme.colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFullPlayer() {
    return Column(
      children: [
        // شريط التنقل العلوي
        _buildAppBar(),

        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: (
              children: [
                // صورة الألبوم الكبيرة
                _buildAlbumArt(),

                const SizedBox(height: 32),

                // معلومات الأغنية
                _buildSongInfo(),

                const SizedBox(height: 32),

                // شريط التقدم
                _buildProgressBar(),

                const SizedBox(height: 32),

                // أزرار التحكم
                _buildControlButtons(),

                const SizedBox(height: 24),

                // أزرار إضافية
                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAppBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: () => Get.back(),
            icon: Icon(
              Icons.keyboard_arrow_down,
              color: Get.theme.colorScheme.onSurface,
              size: 30,
            ),
          ),
          Text(
            'تشغيل الآن',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Get.theme.colorScheme.onSurface,
            ),
          ),
          Row(
            children: [
              IconButton(
                onPressed: () => setState(() => isMinimized = true),
                icon: Icon(
                  Icons.expand_more,
                  color: Get.theme.colorScheme.onSurface,
                ),
              ),
              PopupMenuButton<String>(
                icon: Icon(
                  Icons.more_vert,
                  color: Get.theme.colorScheme.onSurface,
                ),
                onSelected: (value) {
                  switch (value) {
                    case 'add_to_playlist':
                      _showAddToPlaylistDialog();
                      break;
                    case 'share':
                      _shareAudio();
                      break;
                    case 'info':
                      _showAudioInfo();
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'add_to_playlist',
                    child: Row(
                      children: [
                        Icon(Icons.playlist_add, size: 20),
                        SizedBox(width: 8),
                        Text('إضافة لقائمة التشغيل'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'share',
                    child: Row(
                      children: [
                        Icon(Icons.share, size: 20),
                        SizedBox(width: 8),
                        Text('مشاركة'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'info',
                    child: Row(
                      children: [
                        Icon(Icons.info, size: 20),
                        SizedBox(width: 8),
                        Text('معلومات الملف'),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAlbumArt() {
    return Container(
      width: 280,
      height: 280,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Get.theme.colorScheme.primary.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: AnimatedBuilder(
          animation: rotationController,
          builder: (context, child) {
            return Transform.rotate(
              angle: rotationController.value * 2 * pi,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Get.theme.colorScheme.primary,
                      Get.theme.colorScheme.secondary,
                      Get.theme.colorScheme.primary.withValues(alpha: 0.8),
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    // خلفية متحركة
                    AnimatedBuilder(
                      animation: waveController,
                      builder: (context, child) {
                        return CustomPaint(
                          painter: WavePainter(
                            waveController.value,
                            Get.theme.colorScheme.onPrimary
                                .withValues(alpha: 0.3),
                          ),
                          size: const Size(280, 280),
                        );
                      },
                    ),

                    // أيقونة الموسيقى
                    Center(
                      child: Icon(
                        Icons.music_note,
                        size: 80,
                        color: Get.theme.colorScheme.onPrimary,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildSongInfo() {
    return Column(
      children: [
        Text(
          widget.title ?? widget.audioFile.path.split('/').last,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Get.theme.colorScheme.onSurface,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 8),
        Text(
          widget.artist ?? 'غير معروف',
          style: TextStyle(
            fontSize: 16,
            color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildProgressBar() {
    return Column(
      children: [
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: Get.theme.colorScheme.primary,
            inactiveTrackColor:
                Get.theme.colorScheme.primary.withValues(alpha: 0.3),
            thumbColor: Get.theme.colorScheme.primary,
            overlayColor: Get.theme.colorScheme.primary.withValues(alpha: 0.2),
            trackHeight: 4,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
          ),
          child: Slider(
            value: position.inMilliseconds.toDouble(),
            max: duration.inMilliseconds.toDouble(),
            onChanged: (value) {
              _seekTo(Duration(milliseconds: value.toInt()));
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatDuration(position),
                style: TextStyle(
                  color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              Text(
                _formatDuration(duration),
                style: TextStyle(
                  color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildControlButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // زر الخلط
        IconButton(
          onPressed: _toggleShuffle,
          icon: Icon(
            Icons.shuffle,
            color: isShuffled
                ? Get.theme.colorScheme.primary
                : Get.theme.colorScheme.onSurface.withValues(alpha: 0.5),
            size: 28,
          ),
        ),

        // زر السابق
        IconButton(
          onPressed: _playPrevious,
          icon: Icon(
            Icons.skip_previous,
            color: Get.theme.colorScheme.onSurface,
            size: 36,
          ),
        ),

        // زر التشغيل/الإيقاف
        Container(
          width: 70,
          height: 70,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [
                Get.theme.colorScheme.primary,
                Get.theme.colorScheme.secondary,
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Get.theme.colorScheme.primary.withValues(alpha: 0.3),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: IconButton(
            onPressed: _togglePlayPause,
            icon: Icon(
              player.playing ? Icons.pause : Icons.play_arrow,
              color: Get.theme.colorScheme.onPrimary,
              size: 36,
            ),
          ),
        ),

        // زر التالي
        IconButton(
          onPressed: _playNext,
          icon: Icon(
            Icons.skip_next,
            color: Get.theme.colorScheme.onSurface,
            size: 36,
          ),
        ),

        // زر التكرار
        IconButton(
          onPressed: _toggleRepeat,
          icon: Icon(
            Icons.repeat,
            color: isRepeating
                ? Get.theme.colorScheme.primary
                : Get.theme.colorScheme.onSurface.withValues(alpha: 0.5),
            size: 28,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // زر المفضلة
        IconButton(
          onPressed: _toggleFavorite,
          icon: Icon(
            isFavorite ? Icons.favorite : Icons.favorite_border,
            color: isFavorite
                ? Colors.red
                : Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
            size: 28,
          ),
        ),

        // زر إضافة لقائمة التشغيل
        IconButton(
          onPressed: _showAddToPlaylistDialog,
          icon: Icon(
            Icons.playlist_add,
            color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
            size: 28,
          ),
        ),

        // زر المشاركة
        IconButton(
          onPressed: _shareAudio,
          icon: Icon(
            Icons.share,
            color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
            size: 28,
          ),
        ),

        // زر قائمة التشغيل
        IconButton(
          onPressed: _showPlaylistDialog,
          icon: Icon(
            Icons.queue_music,
            color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
            size: 28,
          ),
        ),
      ],
    );
  }

  void _showAddToPlaylistDialog() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        height: Get.height * 0.6,
        decoration: BoxDecoration(
          color: Get.theme.cardColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'إضافة إلى قائمة التشغيل',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Get.theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Obx(() {
                final playlists = playlistController.playlists;
                if (playlists.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.playlist_add,
                          size: 64,
                          color: Get.theme.colorScheme.onSurface
                              .withValues(alpha: 0.5),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد قوائم تشغيل',
                          style: TextStyle(
                            fontSize: 16,
                            color: Get.theme.colorScheme.onSurface
                                .withValues(alpha: 0.7),
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            Get.back();
                            _createNewPlaylist();
                          },
                          child: const Text('إنشاء قائمة جديدة'),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  itemCount: playlists.length + 1,
                  itemBuilder: (context, index) {
                    if (index == 0) {
                      return Card(
                        child: ListTile(
                          leading: Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              color: Get.theme.colorScheme.primary
                                  .withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.add,
                              color: Get.theme.colorScheme.primary,
                            ),
                          ),
                          title: const Text('إنشاء قائمة جديدة'),
                          onTap: () {
                            Get.back();
                            _createNewPlaylist();
                          },
                        ),
                      );
                    }

                    final playlist = playlists[index - 1];
                    return Card(
                      child: ListTile(
                        leading: Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: Get.theme.colorScheme.secondary
                                .withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.music_note,
                            color: Get.theme.colorScheme.secondary,
                          ),
                        ),
                        title: Text(playlist.name),
                        subtitle: Text('${playlist.songIds.length} أغنية'),
                        onTap: () {
                          _addToPlaylist(index - 1);
                          Get.back();
                        },
                      ),
                    );
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  void _createNewPlaylist() {
    final nameController = TextEditingController();
    Get.dialog(
      Dialog(
        backgroundColor: Get.theme.dialogBackgroundColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'إنشاء قائمة تشغيل جديدة',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Get.theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 20),
              TextField(
                controller: nameController,
                decoration: InputDecoration(
                  hintText: 'اسم القائمة',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                        BorderSide(color: Get.theme.colorScheme.primary),
                  ),
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        final name = nameController.text.trim();
                        if (name.isNotEmpty) {
                          playlistController.addPlaylist(name);
                          Get.back();
                          Get.snackbar(
                            'تم',
                            'تم إنشاء قائمة التشغيل بنجاح',
                            backgroundColor: Get.theme.colorScheme.primary,
                            colorText: Get.theme.colorScheme.onPrimary,
                          );
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Get.theme.colorScheme.primary,
                        foregroundColor: Get.theme.colorScheme.onPrimary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('إنشاء'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _addToPlaylist(int playlistIndex) {
    // إضافة الأغنية لقائمة التشغيل
    final mediaItem = MediaItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: widget.title ?? widget.audioFile.path.split('/').last,
      path: widget.audioFile.path,
      type: MediaType.audio,
      artist: widget.artist,
    );

    // هنا يجب إضافة منطق إضافة الأغنية لقائمة التشغيل
    // playlistController.addSongToPlaylist(playlistIndex, mediaItem.id);

    Get.snackbar(
      'تم',
      'تم إضافة الأغنية لقائمة التشغيل',
      backgroundColor: Get.theme.colorScheme.primary,
      colorText: Get.theme.colorScheme.onPrimary,
    );
  }

  void _shareAudio() async {
    try {
      await Share.shareXFiles(
        [XFile(widget.audioFile.path)],
        text:
            'مشاركة ملف صوتي: ${widget.title ?? widget.audioFile.path.split('/').last}',
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في مشاركة الملف',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void _showPlaylistDialog() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        height: Get.height * 0.6,
        decoration: BoxDecoration(
          color: Get.theme.cardColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'قائمة التشغيل الحالية',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Get.theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: playlist.isEmpty
                  ? Center(
                      child: Text(
                        'لا توجد أغاني في قائمة التشغيل',
                        style: TextStyle(
                          color: Get.theme.colorScheme.onSurface
                              .withValues(alpha: 0.7),
                        ),
                      ),
                    )
                  : ListView.builder(
                      itemCount: playlist.length,
                      itemBuilder: (context, index) {
                        final song = playlist[index];
                        final isCurrentSong = index == currentIndex;

                        return Card(
                          color: isCurrentSong
                              ? Get.theme.colorScheme.primary
                                  .withValues(alpha: 0.1)
                              : null,
                          child: ListTile(
                            leading: Container(
                              width: 50,
                              height: 50,
                              decoration: BoxDecoration(
                                color: isCurrentSong
                                    ? Get.theme.colorScheme.primary
                                        .withValues(alpha: 0.2)
                                    : Get.theme.colorScheme.primary
                                        .withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                isCurrentSong
                                    ? Icons.music_note
                                    : Icons.music_note_outlined,
                                color: Get.theme.colorScheme.primary,
                              ),
                            ),
                            title: Text(
                              song.title,
                              style: TextStyle(
                                fontWeight: isCurrentSong
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                                color: isCurrentSong
                                    ? Get.theme.colorScheme.primary
                                    : Get.theme.colorScheme.onSurface,
                              ),
                            ),
                            trailing: isCurrentSong
                                ? Icon(
                                    Icons.equalizer,
                                    color: Get.theme.colorScheme.primary,
                                  )
                                : null,
                            onTap: () {
                              _changeTrack(index);
                              Get.back();
                            },
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAudioInfo() {
    Get.dialog(
      Dialog(
        backgroundColor: Get.theme.dialogBackgroundColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'معلومات الملف',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Get.theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 20),
              _buildInfoRow('الاسم:',
                  widget.title ?? widget.audioFile.path.split('/').last),
              _buildInfoRow('الفنان:', widget.artist ?? 'غير معروف'),
              _buildInfoRow('المدة:', _formatDuration(duration)),
              _buildInfoRow('المسار:', widget.audioFile.path),
              _buildInfoRow('الحجم:',
                  '${(widget.audioFile.lengthSync() / (1024 * 1024)).toStringAsFixed(2)} MB'),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('إغلاق'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Get.theme.colorScheme.onSurface,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// رسام الموجات المتحركة
class WavePainter extends CustomPainter {
  final double animationValue;
  final Color color;

  WavePainter(this.animationValue, this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    final waveHeight = 20.0;
    final waveLength = size.width / 4;

    path.moveTo(0, size.height / 2);

    for (double x = 0; x <= size.width; x += 1) {
      final y = size.height / 2 +
          sin((x / waveLength + animationValue * 2 * pi) * 2 * pi) * waveHeight;
      path.lineTo(x, y);
    }

    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
