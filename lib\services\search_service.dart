import 'package:social_media_app/model/media_item.dart';

class SearchService {
  static List<MediaItem> searchMedia(
    List<MediaItem> items, 
    String query, {
    SearchFilter? filter,
  }) {
    if (query.isEmpty) return items;
    
    return items.where((item) {
      final matchesQuery = item.title.toLowerCase().contains(query.toLowerCase()) ||
                          item.artist?.toLowerCase().contains(query.toLowerCase()) == true;
      
      if (filter != null) {
        return matchesQuery && filter.matches(item);
      }
      
      return matchesQuery;
    }).toList();
  }
}

class SearchFilter {
  final MediaType? type;
  final Duration? minDuration;
  final Duration? maxDuration;
  
  const SearchFilter({this.type, this.minDuration, this.maxDuration});
  
  bool matches(MediaItem item) {
    if (type != null && item.type != type) return false;
    // Add more filter logic
    return true;
  }
}