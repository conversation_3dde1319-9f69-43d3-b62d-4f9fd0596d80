import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';

import 'package:share_plus/share_plus.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';
import 'package:social_media_app/%20controllers/audio_controller.dart';
import 'package:social_media_app/%20controllers/media_controller.dart';
import 'package:social_media_app/%20controllers/audio_playlist_controller.dart';
import 'package:social_media_app/model/media_item.dart';
import 'lyrics_page.dart';

/// صفحة تشغيل الصوت المحسنة مع المشغل المصغر والكبير
/// تدعم الثيمات والتشغيل المتقدم وإضافة للقوائم
class ModernAudioPlayerPage extends StatefulWidget {
  final File audioFile;
  final String? title;
  final String? artist;

  const ModernAudioPlayerPage({
    super.key,
    required this.audioFile,
    this.title,
    this.artist,
  });

  @override
  State<ModernAudioPlayerPage> createState() => _ModernAudioPlayerPageState();
}

class _ModernAudioPlayerPageState extends State<ModernAudioPlayerPage>
    with TickerProviderStateMixin {
  final player = AudioPlayer();
  final panelController = PanelController();

  late AudioController audioController;
  late MediaController mediaController;
  late AnimationController rotationController;

  Duration duration = Duration.zero;
  Duration position = Duration.zero;
  bool isLoading = true;
  bool isFavorite = false;
  List<MediaItem> playlist = [];
  int currentIndex = 0;

  @override
  void initState() {
    super.initState();
    audioController = Get.find<AudioController>();
    mediaController = Get.find<MediaController>();

    rotationController =
        AnimationController(vsync: this, duration: const Duration(seconds: 10));

    _initPlayer();
  }

  void _initPlayer() async {
    // استخدام النظام الموحد
    await audioController.playFromPath(
      widget.audioFile.path,
      title: widget.title,
      artist: widget.artist,
    );

    playlist = mediaController.allAudioFiles;
    currentIndex = playlist.indexWhere((e) => e.path == widget.audioFile.path);
    if (currentIndex == -1) currentIndex = 0;

    // ربط المتغيرات المحلية بالكنترولر
    audioController.duration.listen((d) {
      if (mounted) setState(() => duration = d);
    });

    audioController.currentPosition.listen((p) {
      if (mounted) setState(() => position = p);
    });

    audioController.isPlaying.listen((playing) {
      if (mounted) {
        if (playing) {
          rotationController.repeat();
        } else {
          rotationController.stop();
        }
      }
    });

    if (mounted) setState(() => isLoading = false);
  }

  void _toggleFavorite() {
    final current = playlist[currentIndex];
    setState(() {
      isFavorite = !isFavorite;
    });
    if (isFavorite) {
      mediaController.addToFavorites(current);
    } else {
      mediaController.removeFromFavorites(current);
    }
  }

  void _shareAudio() async {
    try {
      await Share.shareXFiles([XFile(widget.audioFile.path)],
          text: 'استمع لهذه الأغنية!');
    } catch (e) {
      Get.snackbar('خطأ', 'تعذرت مشاركة الملف');
    }
  }

  void _showFileInfo() {
    final file = widget.audioFile;
    final fileSize = (file.lengthSync() / (1024 * 1024)).toStringAsFixed(2);

    Get.dialog(
      AlertDialog(
        title: const Text('معلومات الملف'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الاسم: ${file.path.split('/').last}'),
            Text('الحجم: $fileSize MB'),
            Text('المدة: ${_formatDuration(duration)}'),
            Text('المسار: ${file.path}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration d) {
    String two(int n) => n.toString().padLeft(2, '0');
    return '${two(d.inMinutes)}:${two(d.inSeconds.remainder(60))}';
  }

  @override
  void dispose() {
    player.dispose();
    rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade900,
      body: SlidingUpPanel(
        controller: panelController,
        minHeight: 70,
        maxHeight: MediaQuery.of(context).size.height * 0.9,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        collapsed: _buildMiniPlayer(),
        panel: _buildFullPlayer(),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Column(
              children: [
                _buildHeader(),
                Expanded(
                  child: Center(
                    child: isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : Icon(Icons.music_note,
                            size: 150, color: Colors.white30),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Get.back(),
        ),
        const Spacer(),
        IconButton(
          icon: const Icon(Icons.lyrics, color: Colors.white),
          onPressed: () =>
              Get.to(() => LyricsPage(song: playlist[currentIndex])),
        ),
        IconButton(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          onPressed: () => _showMoreOptions(),
        ),
      ],
    );
  }

  void _showMoreOptions() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading:
                  Icon(isFavorite ? Icons.favorite : Icons.favorite_border),
              title: Text(isFavorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة'),
              onTap: () {
                Get.back();
                _toggleFavorite();
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('مشاركة'),
              onTap: () {
                Get.back();
                _shareAudio();
              },
            ),
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('معلومات الملف'),
              onTap: () {
                Get.back();
                _showFileInfo();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _previousSong() {
    if (currentIndex > 0) {
      currentIndex--;
      _loadSong();
    }
  }

  void _nextSong() {
    if (currentIndex < playlist.length - 1) {
      currentIndex++;
      _loadSong();
    }
  }

  Future<void> _loadSong() async {
    if (currentIndex >= 0 && currentIndex < playlist.length) {
      final item = playlist[currentIndex];
      await player.setAudioSource(AudioSource.uri(Uri.file(item.path)));
      setState(() {});
    }
  }

  Widget _buildMiniPlayer() {
    return Container(
      height: 70,
      decoration: BoxDecoration(
        color: Colors.grey.shade800,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            if (panelController.isAttached) {
              panelController.open();
            }
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                // Album art placeholder
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade600,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.music_note,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                // Song info
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.title ?? 'Unknown Title',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        widget.artist ?? 'Unknown Artist',
                        style: TextStyle(
                          color: Colors.grey.shade300,
                          fontSize: 12,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                // Play/pause button
                IconButton(
                  icon: Icon(
                    player.playing ? Icons.pause : Icons.play_arrow,
                    color: Colors.white,
                    size: 28,
                  ),
                  onPressed: () {
                    player.playing ? player.pause() : player.play();
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFullPlayer() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.deepPurple.shade900,
            Colors.purple.shade800,
            Colors.indigo.shade900,
            Colors.blue.shade900,
          ],
          stops: const [0.0, 0.3, 0.7, 1.0],
        ),
      ),
      child: SingleChildScrollView(
        child: SizedBox(
          height: MediaQuery.of(context).size.height * 0.9,
          child: Column(
            children: [
              // Handle Bar
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 20),
              // Close button
              Align(
                alignment: Alignment.centerRight,
                child: IconButton(
                  icon: const Icon(Icons.keyboard_arrow_down,
                      color: Colors.white, size: 32),
                  onPressed: () => panelController.close(),
                ),
              ),
              const Spacer(),
              // Album art
              Container(
                width: 250,
                height: 250,
                decoration: BoxDecoration(
                  color: Colors.grey.shade700,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.music_note,
                  color: Colors.white,
                  size: 80,
                ),
              ),
              const SizedBox(height: 40),
              // Song info
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Column(
                  children: [
                    Text(
                      widget.title ?? 'Unknown Title',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.artist ?? 'Unknown Artist',
                      style: TextStyle(
                        color: Colors.grey.shade300,
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              const Spacer(),
              // Progress bar
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Column(
                  children: [
                    SliderTheme(
                      data: SliderTheme.of(context).copyWith(
                        activeTrackColor: Colors.white,
                        inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
                        thumbColor: Colors.white,
                        overlayColor: Colors.white.withValues(alpha: 0.2),
                        trackHeight: 3,
                      ),
                      child: Slider(
                        value: position.inMilliseconds.toDouble(),
                        max: duration.inMilliseconds.toDouble(),
                        onChanged: (value) {
                          player.seek(Duration(milliseconds: value.toInt()));
                        },
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          _formatDuration(position),
                          style: TextStyle(
                              color: Colors.grey.shade300, fontSize: 12),
                        ),
                        Text(
                          _formatDuration(duration),
                          style: TextStyle(
                              color: Colors.grey.shade300, fontSize: 12),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              // Control buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  IconButton(
                    icon: const Icon(Icons.skip_previous,
                        color: Colors.white, size: 36),
                    onPressed: _previousSong,
                  ),
                  Container(
                    width: 64,
                    height: 64,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(32),
                    ),
                    child: IconButton(
                      icon: Icon(
                        player.playing ? Icons.pause : Icons.play_arrow,
                        color: Colors.black,
                        size: 32,
                      ),
                      onPressed: () {
                        player.playing ? player.pause() : player.play();
                      },
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.skip_next,
                        color: Colors.white, size: 36),
                    onPressed: _nextSong,
                  ),
                ],
              ),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }
}
