import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart';
import 'package:share_plus/share_plus.dart';
import 'package:social_media_app/%20controllers/media_controller.dart';
import 'package:social_media_app/%20controllers/playlist_controller.dart dart.dart';
import 'package:social_media_app/model/media_item.dart';

/// صفحة تشغيل الفيديو المحسنة مع المشغل المصغر والكبير
/// تدعم الثيمات والتشغيل المتقدم وإضافة للقوائم
class ModernVideoPlayerPage extends StatefulWidget {
  final File videoFile;
  final String? title;

  const ModernVideoPlayerPage({
    super.key,
    required this.videoFile,
    this.title,
  });

  @override
  State<ModernVideoPlayerPage> createState() => _ModernVideoPlayerPageState();
}

class _ModernVideoPlayerPageState extends State<ModernVideoPlayerPage>
    with TickerProviderStateMixin {
  late VideoPlayerController _controller;
  late MediaController mediaController;
  late PlaylistController playlistController;
  late AnimationController _fadeController;
  late AnimationController _scaleController;

  bool _isControlsVisible = true;
  bool _isLoading = true;
  bool _isFullscreen = false;
  bool _isMinimized = false;
  bool _isFavorite = false;
  List<MediaItem> playlist = [];
  int currentIndex = 0;

  @override
  void initState() {
    super.initState();
    mediaController = Get.put(MediaController());
    playlistController = Get.put(PlaylistController());

    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _scaleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _initializePlayer();
    _fadeController.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    super.dispose();
  }

  void _initializePlayer() async {
    try {
      _controller = VideoPlayerController.file(widget.videoFile);
      await _controller.initialize();

      playlist = mediaController.allVideoFiles;
      currentIndex =
          playlist.indexWhere((e) => e.path == widget.videoFile.path);
      if (currentIndex == -1) currentIndex = 0;

      _controller.addListener(() {
        if (mounted) setState(() {});
      });

      _checkIfFavorite();
      setState(() => _isLoading = false);

      // إخفاء الضوابط تلقائياً بعد 3 ثوان
      _hideControlsAfterDelay();
    } catch (e) {
      print('Error initializing video player: $e');
      setState(() => _isLoading = false);
    }
  }

  void _checkIfFavorite() {
    _isFavorite = mediaController.allVideoFiles
        .any((item) => item.path == widget.videoFile.path);
    if (mounted) setState(() {});
  }

  void _toggleFavorite() {
    setState(() => _isFavorite = !_isFavorite);

    Get.snackbar(
      _isFavorite ? 'تم' : 'تم الإزالة',
      _isFavorite ? 'تم إضافة الفيديو للمفضلة' : 'تم إزالة الفيديو من المفضلة',
      backgroundColor: Get.theme.colorScheme.primary,
      colorText: Get.theme.colorScheme.onPrimary,
    );
  }

  void _hideControlsAfterDelay() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && _controller.value.isPlaying) {
        setState(() => _isControlsVisible = false);
      }
    });
  }

  void _toggleControls() {
    setState(() => _isControlsVisible = !_isControlsVisible);
    if (_isControlsVisible) {
      _hideControlsAfterDelay();
    }
  }

  void _togglePlayPause() {
    if (_controller.value.isPlaying) {
      _controller.pause();
    } else {
      _controller.play();
      _hideControlsAfterDelay();
    }
  }

  void _seekTo(Duration position) {
    _controller.seekTo(position);
  }

  void _playNext() {
    if (playlist.isEmpty) return;

    int nextIndex = (currentIndex + 1) % playlist.length;
    _changeVideo(nextIndex);
  }

  void _playPrevious() {
    if (playlist.isEmpty) return;

    int prevIndex = (currentIndex - 1);
    if (prevIndex < 0) prevIndex = playlist.length - 1;

    _changeVideo(prevIndex);
  }

  void _changeVideo(int index) async {
    if (index < 0 || index >= playlist.length) return;

    setState(() {
      currentIndex = index;
      _isLoading = true;
    });

    try {
      await _controller.dispose();
      _controller = VideoPlayerController.file(File(playlist[index].path));
      await _controller.initialize();

      _controller.addListener(() {
        if (mounted) setState(() {});
      });

      await _controller.play();
      _checkIfFavorite();
      _hideControlsAfterDelay();
    } catch (e) {
      print('Error changing video: $e');
    }

    setState(() => _isLoading = false);
  }

  void _toggleFullscreen() {
    setState(() => _isFullscreen = !_isFullscreen);

    if (_isFullscreen) {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
    } else {
      SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: _isMinimized ? _buildMiniPlayer() : _buildFullPlayer(),
    );
  }

  Widget _buildMiniPlayer() {
    return Container(
      height: 70,
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Get.theme.cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // معاينة الفيديو المصغرة
          Container(
            width: 60,
            height: 60,
            margin: const EdgeInsets.all(5),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.black,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : AspectRatio(
                      aspectRatio: _controller.value.aspectRatio,
                      child: VideoPlayer(_controller),
                    ),
            ),
          ),

          // معلومات الفيديو
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.title ?? widget.videoFile.path.split('/').last,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Get.theme.colorScheme.onSurface,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    _formatDuration(_controller.value.duration),
                    style: TextStyle(
                      color: Get.theme.colorScheme.onSurface
                          .withValues(alpha: 0.7),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // أزرار التحكم
          IconButton(
            onPressed: _playPrevious,
            icon: Icon(
              Icons.skip_previous,
              color: Get.theme.colorScheme.primary,
            ),
          ),

          IconButton(
            onPressed: _togglePlayPause,
            icon: Icon(
              _controller.value.isPlaying ? Icons.pause : Icons.play_arrow,
              color: Get.theme.colorScheme.primary,
              size: 30,
            ),
          ),

          IconButton(
            onPressed: _playNext,
            icon: Icon(
              Icons.skip_next,
              color: Get.theme.colorScheme.primary,
            ),
          ),

          IconButton(
            onPressed: () => setState(() => _isMinimized = false),
            icon: Icon(
              Icons.expand_less,
              color: Get.theme.colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFullPlayer() {
    return Stack(
      children: [
        // مشغل الفيديو
        Center(
          child: _isLoading
              ? const CircularProgressIndicator()
              : GestureDetector(
                  onTap: _toggleControls,
                  child: AspectRatio(
                    aspectRatio: _controller.value.aspectRatio,
                    child: VideoPlayer(_controller),
                  ),
                ),
        ),

        // الضوابط
        if (_isControlsVisible) _buildControls(),

        // شريط التقدم السفلي
        if (!_isControlsVisible) _buildBottomProgressBar(),
      ],
    );
  }

  Widget _buildControls() {
    return AnimatedBuilder(
      animation: _fadeController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeController,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withValues(alpha: 0.7),
                  Colors.transparent,
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.7),
                ],
              ),
            ),
            child: SafeArea(
              child: Column(
                children: [
                  // شريط التنقل العلوي
                  _buildTopBar(),

                  Expanded(
                    child: Center(
                      child: _buildPlayControls(),
                    ),
                  ),

                  // شريط التحكم السفلي
                  _buildBottomControls(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTopBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(
              Icons.arrow_back,
              color: Colors.white,
              size: 28,
            ),
          ),
          Expanded(
            child: Text(
              widget.title ?? widget.videoFile.path.split('/').last,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Row(
            children: [
              IconButton(
                onPressed: () => setState(() => _isMinimized = true),
                icon: const Icon(
                  Icons.picture_in_picture,
                  color: Colors.white,
                ),
              ),
              PopupMenuButton<String>(
                icon: const Icon(
                  Icons.more_vert,
                  color: Colors.white,
                ),
                onSelected: (value) {
                  switch (value) {
                    case 'add_to_playlist':
                      _showAddToPlaylistDialog();
                      break;
                    case 'share':
                      _shareVideo();
                      break;
                    case 'info':
                      _showVideoInfo();
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'add_to_playlist',
                    child: Row(
                      children: [
                        Icon(Icons.playlist_add, size: 20),
                        SizedBox(width: 8),
                        Text('إضافة لقائمة التشغيل'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'share',
                    child: Row(
                      children: [
                        Icon(Icons.share, size: 20),
                        SizedBox(width: 8),
                        Text('مشاركة'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'info',
                    child: Row(
                      children: [
                        Icon(Icons.info, size: 20),
                        SizedBox(width: 8),
                        Text('معلومات الملف'),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPlayControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // زر السابق
        ScaleTransition(
          scale: _scaleController,
          child: IconButton(
            onPressed: _playPrevious,
            icon: const Icon(
              Icons.skip_previous,
              color: Colors.white,
              size: 40,
            ),
          ),
        ),

        // زر التشغيل/الإيقاف
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Get.theme.colorScheme.primary.withValues(alpha: 0.8),
            boxShadow: [
              BoxShadow(
                color: Get.theme.colorScheme.primary.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: IconButton(
            onPressed: _togglePlayPause,
            icon: Icon(
              _controller.value.isPlaying ? Icons.pause : Icons.play_arrow,
              color: Colors.white,
              size: 40,
            ),
          ),
        ),

        // زر التالي
        ScaleTransition(
          scale: _scaleController,
          child: IconButton(
            onPressed: _playNext,
            icon: const Icon(
              Icons.skip_next,
              color: Colors.white,
              size: 40,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomControls() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // شريط التقدم
          Row(
            children: [
              Text(
                _formatDuration(_controller.value.position),
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
              Expanded(
                child: Slider(
                  value: _controller.value.position.inMilliseconds.toDouble(),
                  max: _controller.value.duration.inMilliseconds.toDouble(),
                  onChanged: (value) {
                    _seekTo(Duration(milliseconds: value.toInt()));
                  },
                  activeColor: Get.theme.colorScheme.primary,
                  inactiveColor: Colors.white.withValues(alpha: 0.3),
                ),
              ),
              Text(
                _formatDuration(_controller.value.duration),
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
            ],
          ),

          // أزرار إضافية
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                onPressed: _toggleFavorite,
                icon: Icon(
                  _isFavorite ? Icons.favorite : Icons.favorite_border,
                  color: _isFavorite ? Colors.red : Colors.white,
                ),
              ),
              IconButton(
                onPressed: _showAddToPlaylistDialog,
                icon: const Icon(
                  Icons.playlist_add,
                  color: Colors.white,
                ),
              ),
              IconButton(
                onPressed: _shareVideo,
                icon: const Icon(
                  Icons.share,
                  color: Colors.white,
                ),
              ),
              IconButton(
                onPressed: _toggleFullscreen,
                icon: Icon(
                  _isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBottomProgressBar() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: LinearProgressIndicator(
        value: _controller.value.duration.inMilliseconds > 0
            ? _controller.value.position.inMilliseconds /
                _controller.value.duration.inMilliseconds
            : 0.0,
        backgroundColor: Colors.white.withValues(alpha: 0.3),
        valueColor:
            AlwaysStoppedAnimation<Color>(Get.theme.colorScheme.primary),
        minHeight: 3,
      ),
    );
  }

  void _showAddToPlaylistDialog() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        height: Get.height * 0.6,
        decoration: BoxDecoration(
          color: Get.theme.cardColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'إضافة إلى قائمة التشغيل',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Get.theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Obx(() {
                final playlists = playlistController.playlists;
                if (playlists.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.playlist_add,
                          size: 64,
                          color: Get.theme.colorScheme.onSurface
                              .withValues(alpha: 0.5),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد قوائم تشغيل',
                          style: TextStyle(
                            fontSize: 16,
                            color: Get.theme.colorScheme.onSurface
                                .withValues(alpha: 0.7),
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            Get.back();
                            _createNewPlaylist();
                          },
                          child: const Text('إنشاء قائمة جديدة'),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  itemCount: playlists.length + 1,
                  itemBuilder: (context, index) {
                    if (index == 0) {
                      return Card(
                        child: ListTile(
                          leading: Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              color: Get.theme.colorScheme.primary
                                  .withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.add,
                              color: Get.theme.colorScheme.primary,
                            ),
                          ),
                          title: const Text('إنشاء قائمة جديدة'),
                          onTap: () {
                            Get.back();
                            _createNewPlaylist();
                          },
                        ),
                      );
                    }

                    final playlist = playlists[index - 1];
                    return Card(
                      child: ListTile(
                        leading: Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: Get.theme.colorScheme.secondary
                                .withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.video_library,
                            color: Get.theme.colorScheme.secondary,
                          ),
                        ),
                        title: Text(playlist.name),
                        subtitle: Text('${playlist.videoPaths.length} فيديو'),
                        onTap: () {
                          _addToPlaylist(index - 1);
                          Get.back();
                        },
                      ),
                    );
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  void _createNewPlaylist() {
    final nameController = TextEditingController();
    Get.dialog(
      Dialog(
        backgroundColor: Get.theme.dialogBackgroundColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'إنشاء قائمة تشغيل جديدة',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Get.theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 20),
              TextField(
                controller: nameController,
                decoration: InputDecoration(
                  hintText: 'اسم القائمة',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                        BorderSide(color: Get.theme.colorScheme.primary),
                  ),
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        final name = nameController.text.trim();
                        if (name.isNotEmpty) {
                          playlistController.addPlaylist(name);
                          Get.back();
                          Get.snackbar(
                            'تم',
                            'تم إنشاء قائمة التشغيل بنجاح',
                            backgroundColor: Get.theme.colorScheme.primary,
                            colorText: Get.theme.colorScheme.onPrimary,
                          );
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Get.theme.colorScheme.primary,
                        foregroundColor: Get.theme.colorScheme.onPrimary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('إنشاء'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _addToPlaylist(int playlistIndex) {
    playlistController.addVideoToPlaylist(playlistIndex, widget.videoFile.path);

    Get.snackbar(
      'تم',
      'تم إضافة الفيديو لقائمة التشغيل',
      backgroundColor: Get.theme.colorScheme.primary,
      colorText: Get.theme.colorScheme.onPrimary,
    );
  }

  void _shareVideo() async {
    try {
      await Share.shareXFiles(
        [XFile(widget.videoFile.path)],
        text:
            'مشاركة ملف فيديو: ${widget.title ?? widget.videoFile.path.split('/').last}',
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في مشاركة الملف',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void _showVideoInfo() {
    Get.dialog(
      Dialog(
        backgroundColor: Get.theme.dialogBackgroundColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'معلومات الملف',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Get.theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 20),
              _buildInfoRow('الاسم:',
                  widget.title ?? widget.videoFile.path.split('/').last),
              _buildInfoRow(
                  'المدة:', _formatDuration(_controller.value.duration)),
              _buildInfoRow('الدقة:',
                  '${_controller.value.size.width.toInt()}x${_controller.value.size.height.toInt()}'),
              _buildInfoRow('المسار:', widget.videoFile.path),
              _buildInfoRow('الحجم:',
                  '${(widget.videoFile.lengthSync() / (1024 * 1024)).toStringAsFixed(2)} MB'),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('إغلاق'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Get.theme.colorScheme.onSurface,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
