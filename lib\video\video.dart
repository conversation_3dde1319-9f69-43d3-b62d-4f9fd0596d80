// import 'dart:io';
//
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:photo_manager/photo_manager.dart';
// import 'package:video_player/video_player.dart';
//
// void main() {
//   runApp(const MyApp());
// }
//
// class MyApp extends StatelessWidget {
//   const MyApp({super.key});
//   @override
//   Widget build(BuildContext context) {
//     return GetMaterialApp(
//       title: 'Media Player',
//       home: const VideoScreen(),
//       debugShowCheckedModeBanner: false,
//     );
//   }
// }
//
// class Playlist {
//   final String name;
//   final List<File> videos;
//
//   Playlist({required this.name, this.videos = const []});
// }
//
// class VideoScreen extends StatefulWidget {
//   const VideoScreen({super.key});
//
//   @override
//   State<VideoScreen> createState() => _VideoScreenState();
// }
//
// class _VideoScreenState extends State<VideoScreen> {
//   List<AssetEntity> videoFiles = [];
//   List<Playlist> playlists = [];
//
//   @override
//   void initState() {
//     super.initState();
//     _loadAllVideos();
//   }
//
//   Future<void> _loadAllVideos() async {
//     final permission = await PhotoManager.requestPermissionExtend(
//       requestOption: const PermissionRequestOption(
//         iosAccessLevel: IosAccessLevel.readWrite,
//       ),
//     );
//
//     ScaffoldMessenger.of(context).showSnackBar(
//       SnackBar(
//         content: Text(permission.isAuth
//             ? 'تم منح الإذن بالوصول إلى الفيديوهات'
//             : 'تم رفض الإذن بالوصول إلى الفيديوهات'),
//         duration: const Duration(seconds: 2),
//       ),
//     );
//
//     if (permission.isAuth) {
//       List<AssetPathEntity> albums = await PhotoManager.getAssetPathList(
//         type: RequestType.video,
//         onlyAll: true,
//       );
//
//       final recentAlbum = albums.first;
//       final files = await recentAlbum.getAssetListPaged(page: 0, size: 1000);
//
//       setState(() {
//         videoFiles = files;
//       });
//     } else {
//       await PhotoManager.openSetting();
//     }
//   }
//
//   void addToPlaylist(File video) {
//     showModalBottomSheet(
//       context: context,
//       builder: (_) {
//         return ListView(
//           shrinkWrap: true,
//           children: [
//             ...playlists.map((playlist) => ListTile(
//               title: Text(playlist.name),
//               onTap: () {
//                 setState(() {
//                   playlist.videos.add(video);
//                 });
//                 Navigator.pop(context);
//                 ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//                     content: Text(
//                         'تمت الإضافة إلى قائمة التشغيل "${playlist.name}"')));
//               },
//             )),
//             ListTile(
//               leading: const Icon(Icons.add),
//               title: const Text("إنشاء قائمة تشغيل جديدة"),
//               onTap: () {
//                 Navigator.pop(context);
//                 _createNewPlaylist(video);
//               },
//             ),
//           ],
//         );
//       },
//     );
//   }
//
//   void _createNewPlaylist(File video) {
//     final controller = TextEditingController();
//     showDialog(
//       context: context,
//       builder: (_) => AlertDialog(
//         title: const Text('اسم القائمة'),
//         content: TextField(controller: controller, autofocus: true),
//         actions: [
//           TextButton(
//             onPressed: () {
//               final name = controller.text.trim();
//               if (name.isNotEmpty) {
//                 setState(() {
//                   playlists.add(Playlist(name: name, videos: [video]));
//                 });
//                 Navigator.pop(context);
//                 ScaffoldMessenger.of(context).showSnackBar(
//                     SnackBar(content: Text('تم إنشاء القائمة "$name"')));
//               }
//             },
//             child: const Text('حفظ'),
//           ),
//         ],
//       ),
//     );
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('جميع الفيديوهات'),
//         actions: [
//           if (playlists.isNotEmpty)
//             IconButton(
//               icon: const Icon(Icons.playlist_play),
//               tooltip: 'قوائم التشغيل',
//               onPressed: () {
//                 Get.to(() => PlaylistsScreen(playlists: playlists));
//               },
//             )
//         ],
//       ),
//       body: ListView.builder(
//         itemCount: videoFiles.length,
//         itemBuilder: (context, index) {
//           final video = videoFiles[index];
//           return FutureBuilder<File?>(
//             future: video.file,
//             builder: (context, snapshot) {
//               if (!snapshot.hasData) return const SizedBox();
//               final file = snapshot.data!;
//               return ListTile(
//                 leading: const Icon(Icons.video_library),
//                 title: Text(file.path.split('/').last),
//                 trailing: IconButton(
//                   icon: const Icon(Icons.playlist_add),
//                   tooltip: 'إضافة لقائمة تشغيل',
//                   onPressed: () {
//                     addToPlaylist(file);
//                   },
//                 ),
//                 onTap: () {
//                   Get.to(() => VideoPlayerScreen(
//                     playlistVideos: [file],
//                     initialIndex: 0,
//                   ));
//                 },
//               );
//             },
//           );
//         },
//       ),
//     );
//   }
// }
//
// // شاشة قوائم التشغيل
// class PlaylistsScreen extends StatelessWidget {
//   final List<Playlist> playlists;
//
//   const PlaylistsScreen({super.key, required this.playlists});
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(title: const Text('قوائم التشغيل')),
//       body: ListView.builder(
//         itemCount: playlists.length,
//         itemBuilder: (context, index) {
//           final playlist = playlists[index];
//           return ListTile(
//             title: Text(playlist.name),
//             subtitle: Text('${playlist.videos.length} فيديو'),
//             onTap: () {
//               Get.to(() => VideoPlayerScreen(
//                 playlistVideos: playlist.videos,
//                 initialIndex: 0,
//               ));
//             },
//           );
//         },
//       ),
//     );
//   }
// }
//
// // شاشة تشغيل الفيديو مع دعم قائمة التشغيل، شريط التقدم، الأزرار التالية والسابقة، والوقت
// class VideoPlayerScreen extends StatefulWidget {
//   final List<File> playlistVideos;
//   final int initialIndex;
//
//   const VideoPlayerScreen({
//     super.key,
//     required this.playlistVideos,
//     this.initialIndex = 0,
//   });
//
//   @override
//   State<VideoPlayerScreen> createState() => _VideoPlayerScreenState();
// }
//
// class _VideoPlayerScreenState extends State<VideoPlayerScreen> {
//   late VideoPlayerController _controller;
//   int currentIndex = 0;
//   bool isPlaying = false;
//   bool isDisposed = false;
//
//   @override
//   void initState() {
//     super.initState();
//     currentIndex = widget.initialIndex;
//     _initVideo(widget.playlistVideos[currentIndex]);
//   }
//
//   Future<void> _initVideo(File file) async {
//     _controller = VideoPlayerController.file(file);
//     await _controller.initialize();
//     if (isDisposed) return;
//     setState(() {
//       isPlaying = true;
//     });
//     _controller.play();
//
//     _controller.addListener(() {
//       if (_controller.value.position >= _controller.value.duration &&
//           !_controller.value.isPlaying) {
//         _playNext();
//       }
//       if (!isDisposed) setState(() {});
//     });
//   }
//
//   void _playNext() {
//     if (currentIndex + 1 < widget.playlistVideos.length) {
//       currentIndex++;
//       _controller.dispose();
//       _initVideo(widget.playlistVideos[currentIndex]);
//     } else {
//       // نهاية القائمة
//       ScaffoldMessenger.of(context).showSnackBar(
//         const SnackBar(content: Text('تم تشغيل كل الفيديوهات في القائمة')),
//       );
//     }
//   }
//
//   void _playPrevious() {
//     if (currentIndex - 1 >= 0) {
//       currentIndex--;
//       _controller.dispose();
//       _initVideo(widget.playlistVideos[currentIndex]);
//     }
//   }
//
//   @override
//   void dispose() {
//     isDisposed = true;
//     _controller.dispose();
//     super.dispose();
//   }
//
//   String _formatDuration(Duration d) {
//     String twoDigits(int n) => n.toString().padLeft(2, '0');
//     final minutes = twoDigits(d.inMinutes.remainder(60));
//     final seconds = twoDigits(d.inSeconds.remainder(60));
//     return '${twoDigits(d.inHours)}:$minutes:$seconds';
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     final fileName = widget.playlistVideos[currentIndex].path.split('/').last;
//
//     return Scaffold(
//       appBar: AppBar(
//         title: Text(fileName),
//       ),
//       body: Column(
//         children: [
//           if (_controller.value.isInitialized)
//             AspectRatio(
//               aspectRatio: _controller.value.aspectRatio,
//               child: VideoPlayer(_controller),
//             )
//           else
//             const Expanded(
//               child: Center(child: CircularProgressIndicator()),
//             ),
//           VideoProgressIndicator(
//             _controller,
//             allowScrubbing: true,
//             padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
//           ),
//           Padding(
//             padding: const EdgeInsets.symmetric(horizontal: 16),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Text(_formatDuration(_controller.value.position)),
//                 Text(_formatDuration(_controller.value.duration)),
//               ],
//             ),
//           ),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               IconButton(
//                 icon: const Icon(Icons.skip_previous),
//                 iconSize: 36,
//                 onPressed: currentIndex > 0 ? _playPrevious : null,
//               ),
//               IconButton(
//                 icon: Icon(isPlaying ? Icons.pause : Icons.play_arrow),
//                 iconSize: 48,
//                 onPressed: () {
//                   setState(() {
//                     if (isPlaying) {
//                       _controller.pause();
//                     } else {
//                       _controller.play();
//                     }
//                     isPlaying = !isPlaying;
//                   });
//                 },
//               ),
//               IconButton(
//                 icon: const Icon(Icons.skip_next),
//                 iconSize: 36,
//                 onPressed: currentIndex < widget.playlistVideos.length - 1
//                     ? _playNext
//                     : null,
//               ),
//             ],
//           ),
//           const SizedBox(height: 16),
//         ],
//       ),
//     );
//   }
// }
