import 'dart:io';

enum MediaType { audio, video }

class MediaItem {
  final String id;
  final String title;
  final String path;
  final String? artist;
  final String? album;
  final Duration? duration;
  final MediaType type;
  final String? thumbnail;
  final int? size;
  final DateTime? dateAdded;
  final String? genre;
  final int? bitrate;

  MediaItem({
    required this.id,
    required this.title,
    required this.path,
    this.artist,
    this.album,
    this.duration,
    required this.type,
    this.thumbnail,
    this.size,
    this.dateAdded,
    this.genre,
    this.bitrate,
  });

  String get displayName => title.isNotEmpty ? title : path.split('/').last;
  String get displayArtist => artist ?? 'Unknown Artist';
  String get displayAlbum => album ?? 'Unknown Album';
  File get file => File(path);

  // Add serialization methods
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'path': path,
      'artist': artist,
      'album': album,
      'duration': duration?.inMilliseconds,
      'type': type.name,
      'thumbnail': thumbnail,
      'size': size,
      'dateAdded': dateAdded?.millisecondsSinceEpoch,
      'genre': genre,
      'bitrate': bitrate,
    };
  }

  factory MediaItem.fromJson(Map<String, dynamic> json) {
    return MediaItem(
      id: json['id'],
      title: json['title'],
      path: json['path'],
      artist: json['artist'],
      album: json['album'],
      duration: json['duration'] != null
          ? Duration(milliseconds: json['duration'])
          : null,
      type: MediaType.values.firstWhere((e) => e.name == json['type']),
      thumbnail: json['thumbnail'],
      size: json['size'],
      dateAdded: json['dateAdded'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['dateAdded'])
          : null,
      genre: json['genre'],
      bitrate: json['bitrate'],
    );
  }
}
