// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'audio_playlist_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AudioPlaylistAdapter extends TypeAdapter<AudioPlaylist> {
  @override
  final int typeId = 1;

  @override
  AudioPlaylist read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AudioPlaylist(
      name: fields[0] as String,
      songIds: (fields[1] as List).cast<int>(),
    );
  }

  @override
  void write(BinaryWriter writer, AudioPlaylist obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.name)
      ..writeByte(1)
      ..write(obj.songIds);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AudioPlaylistAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
