import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:social_media_app/%20controllers/audio_controller.dart';
import 'package:social_media_app/%20controllers/audio_playlist_controller.dart';
import 'package:social_media_app/widgets/mini_player.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

import '../widgets/modern_full_player.dart';

class AudioListPage extends StatelessWidget {
  final controller = Get.put(AudioController());
  final playlistController = Get.put(AudioPlaylistController());
  final PanelController panelController = PanelController();

  AudioListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // المحتوى الرئيسي
          Obx(() {
            if (controller.allSongs.isEmpty) {
              return const Center(child: CircularProgressIndicator());
            }

            return ListView.builder(
              padding: EdgeInsets.only(
                bottom: controller.currentSong.value != null ? 140 : 0,
              ),
              itemCount: controller.allSongs.length,
              itemBuilder: (context, index) {
                final song = controller.allSongs[index];
                return ListTile(
                  title: Text(song.title),
                  subtitle: Text(song.artist ?? "مجهول"),
                  onTap: () {
                    controller.playSong(song);
                  },
                );
              },
            );
          }),

          // المشغل المصغر والكامل
          Obx(() {
            if (controller.currentSong.value == null) {
              return const SizedBox.shrink();
            }

            return SlidingUpPanel(
              controller: panelController,
              minHeight: 70,
              maxHeight: MediaQuery.of(context).size.height * 0.9,
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(16)),
              panel: FullPlayer(panelController: panelController),
              collapsed: MiniPlayer(),
              body: const SizedBox.shrink(),
            );
          }),
        ],
      ),
    );
  }
}
