// import 'package:just_audio/just_audio.dart';
//
// class AudioPlayerHandler extends BaseAudioHandler {
//   final _player = AudioPlayer();
//   final _playlist = ConcatenatingAudioSource(children: []);
//
//   AudioPlayerHandler() {
//     _init();
//   }
//
//   Future<void> _init() async {
//     _player.playbackEventStream.map(_transformEvent).pipe(playbackState);
//     mediaItem.add(MediaItem(id: '1', title: 'Initial'));
//   }
//
//   PlaybackState _transformEvent(PlaybackEvent event) {
//     return PlaybackState(
//       controls: [
//         MediaControl.play,
//         MediaControl.pause,
//         MediaControl.skipToNext,
//         MediaControl.skipToPrevious,
//       ],
//       systemActions: const {
//         MediaAction.seek,
//         MediaAction.seekForward,
//         MediaAction.seekBackward,
//       },
//       androidCompactActionIndices: const [0, 1, 3],
//       processingState: const {
//         ProcessingState.idle: AudioProcessingState.idle,
//         ProcessingState.loading: AudioProcessingState.loading,
//         ProcessingState.buffering: AudioProcessingState.buffering,
//         ProcessingState.ready: AudioProcessingState.ready,
//         ProcessingState.completed: AudioProcessingState.completed,
//       }[_player.processingState]!,
//       playing: _player.playing,
//       updatePosition: _player.position,
//       bufferedPosition: _player.bufferedPosition,
//       speed: _player.speed,
//       queueIndex: event.currentIndex,
//     );
//   }
//
//   @override
//   Future<void> play() => _player.play();
//   @override
//   Future<void> pause() => _player.pause();
//   @override
//   Future<void> seek(Duration position) => _player.seek(position);
//   @override
//   Future<void> skipToNext() => _player.seekToNext();
//   @override
//   Future<void> skipToPrevious() => _player.seekToPrevious();
// }