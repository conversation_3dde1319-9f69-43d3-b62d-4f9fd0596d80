import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../ controllers/media_controller.dart';
import 'video_player_page.dart';

class VideoListPage extends StatelessWidget {
  const VideoListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final mediaController = Get.find<MediaController>();

    return Obx(() {
      if (mediaController.videos.isEmpty) {
        return const Center(child: Text('لا توجد فيديوهات'));
      }
      return GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.8,
        ),
        itemCount: mediaController.videos.length,
        itemBuilder: (context, index) {
          final video = mediaController.videos[index];
          return FutureBuilder<File?>(
            future: video.file,
            builder: (context, snapshot) {
              if (!snapshot.hasData) return const SizedBox();
              final file = snapshot.data!;
              return InkWell(
                onTap: () {
                  Get.to(() => VideoPlayerPage(videoFile: file));
                },
                child: Card(
                  child: Column(
                    children: [
                      Expanded(
                        child: Stack(
                          fit: StackFit.expand,
                          children: [
                            Image.file(
                              file,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                              const Icon(Icons.videocam),
                            ),
                            const Align(
                              alignment: Alignment.center,
                              child: Icon(Icons.play_circle_fill,
                                  color: Colors.white, size: 50),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          file.path.split('/').last,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      );
    });
  }
}