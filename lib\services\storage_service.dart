import 'dart:convert';

import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:social_media_app/model/media_item.dart';
class StorageService extends GetxService {
  late SharedPreferences _prefs;
  
  Future<StorageService> init() async {
    _prefs = await SharedPreferences.getInstance();
    return this;
  }
  
  // Save/Load favorites
  void saveFavorites(List<MediaItem> favorites) {
    final json = favorites.map((e) => e.toJson()).toList();
    _prefs.setString('favorites', jsonEncode(json));
  }
  
  List<MediaItem> loadFavorites() {
    final data = _prefs.getString('favorites');
    if (data != null) {
      final List<dynamic> json = jsonDecode(data);
      return json.map((e) => MediaItem.fromJson(e)).toList();
    }
    return [];
  }
}