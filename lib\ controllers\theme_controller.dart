import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import '../themes/app_themes.dart';

class ThemeController extends GetxController {
  static const String _themeKey = 'selected_theme';
  late Box _box;

  final RxString _currentTheme = 'dark'.obs;
  String get currentTheme => _currentTheme.value;

  var currentThemeName = 'البرتقالي الداكن'.obs;

  final List<Map<String, dynamic>> availableThemes = [
    {
      'name': 'orange_dark',
      'title': 'البرتقالي الداكن',
      'color': Colors.orange,
    },
    {
      'name': 'blue_dark',
      'title': 'الأزرق الداكن',
      'color': Colors.blue,
    },
    {
      'name': 'purple_dark',
      'title': 'البنفسجي الداكن',
      'color': Colors.purple,
    },
    {
      'name': 'green_dark',
      'title': 'الأخضر الداكن',
      'color': Colors.green,
    },
    {
      'name': 'red_dark',
      'title': 'الأحمر الداكن',
      'color': Colors.red,
    },
  ];

  @override
  void onInit() {
    super.onInit();
    _initHive();
  }

  Future<void> _initHive() async {
    _box = await Hive.openBox('settings');
    _loadTheme();
  }

  void _loadTheme() {
    final savedTheme = _box.get(_themeKey, defaultValue: 'orange_dark');
    _currentTheme.value = savedTheme;

    // تحديث اسم الثيم المحفوظ
    final theme =
        availableThemes.firstWhereOrNull((t) => t['name'] == savedTheme);
    if (theme != null) {
      currentThemeName.value = theme['title'];
    }

    _applyTheme(savedTheme);
  }

  void changeTheme(String themeName) {
    _currentTheme.value = themeName;
    final theme = availableThemes.firstWhere((t) => t['name'] == themeName);
    currentThemeName.value = theme['title'];
    _box.put(_themeKey, themeName);
    _applyTheme(themeName);
  }

  void _applyTheme(String themeName) {
    switch (themeName) {
      case 'light':
        Get.changeTheme(AppThemes.lightTheme);
        break;
      case 'purple_dark':
        Get.changeTheme(AppThemes.purpleTheme);
        break;
      case 'green_dark':
        Get.changeTheme(AppThemes.greenTheme);
        break;
      case 'orange_dark':
        Get.changeTheme(AppThemes.orangeTheme);
        break;
      case 'blue_dark':
        Get.changeTheme(AppThemes.blueTheme);
        break;
      case 'red_dark':
        Get.changeTheme(AppThemes.redTheme);
        break;
      default:
        Get.changeTheme(AppThemes.darkTheme);
    }
  }
}
