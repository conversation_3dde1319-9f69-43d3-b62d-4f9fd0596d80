import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';

import ' controllers/audio_controller.dart';
import ' controllers/audio_playlist_controller.dart';
import ' controllers/media_controller.dart';
import ' controllers/playlist_controller.dart dart.dart';
import 'model/audio_playlist_model.dart';
import 'model/playlist_model.dart';
import 'pages/main_navigation_page.dart';
import ' controllers/theme_controller.dart';
import 'themes/app_themes.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Hive.initFlutter();
  Hive.registerAdapter(AudioPlaylistAdapter());
  Hive.registerAdapter(PlaylistAdapter());
  await Hive.openBox<Playlist>('playlists');
  await Hive.openBox<AudioPlaylist>('audioPlaylists');

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // إنشاء كنترولر الثيم أولاً
    final themeController = Get.put(ThemeController());

    return Obx(() => GetMaterialApp(
          title: 'Lark Player',
          debugShowCheckedModeBanner: false,
          // استخدام الثيم الديناميكي من الكنترولر
          theme: _getThemeFromController(themeController),
          home: const MainNavigationPage(),
          initialBinding: BindingsBuilder(() {
            Get.put(PlaylistController());
            Get.put(MediaController());
            Get.put(AudioController());
            Get.put(AudioPlaylistController());
          }),
        ));
  }

  /// دالة للحصول على الثيم من الكنترولر
  ThemeData _getThemeFromController(ThemeController controller) {
    switch (controller.currentTheme) {
      case 'light':
        return AppThemes.lightTheme;
      case 'purple_dark':
        return AppThemes.purpleTheme;
      case 'green_dark':
        return AppThemes.greenTheme;
      case 'orange_dark':
        return AppThemes.orangeTheme;
      case 'blue_dark':
        return AppThemes.blueTheme;
      case 'red_dark':
        return AppThemes.redTheme;
      default:
        return AppThemes.darkTheme;
    }
  }
}
