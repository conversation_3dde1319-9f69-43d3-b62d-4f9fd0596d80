import 'dart:typed_data';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import 'package:on_audio_query_forked/on_audio_query.dart';
import '../model/media_item.dart';

class AudioController extends GetxController {
  final audioPlayer = AudioPlayer();
  final audioQuery = OnAudioQuery();

  var allSongs = <SongModel>[].obs;
  var currentSong = Rxn<SongModel>();
  var currentMediaItem = Rxn<MediaItem>();
  var playlist = <dynamic>[].obs;
  var currentIndex = 0.obs;

  var isPlaying = false.obs;
  var isRepeating = false.obs;
  var isShuffleEnabled = false.obs;
  var currentPosition = Duration.zero.obs;
  var duration = Duration.zero.obs;
  var isLoading = false.obs;

  // إعدادات التشغيل الإضافية
  var autoPlayNext = true.obs;
  var repeatMode = 'none'.obs; // none, one, all

  // إضافات جديدة للتحكم الموحد
  var isMinimized = false.obs;
  var showLyrics = false.obs;
  var playbackSpeed = 1.0.obs;

  @override
  void onInit() {
    super.onInit();
    _setupListeners();
    loadSongs();
  }

  void _setupListeners() {
    audioPlayer.positionStream.listen((pos) {
      currentPosition.value = pos;
    });

    audioPlayer.durationStream.listen((dur) {
      duration.value = dur ?? Duration.zero;
    });

    audioPlayer.playerStateStream.listen((state) {
      isPlaying.value = state.playing;
      isLoading.value = state.processingState == ProcessingState.loading;

      if (state.processingState == ProcessingState.completed) {
        if (repeatMode.value == 'one') {
          replayCurrent();
        } else if (autoPlayNext.value && repeatMode.value != 'none') {
          playNext();
        } else if (autoPlayNext.value) {
          playNext();
        }
      }
    });
  }

  Future<void> loadSongs() async {
    bool permission = await audioQuery.permissionsStatus();
    if (!permission) {
      permission = await audioQuery.permissionsRequest();
    }

    if (permission) {
      final songs = await audioQuery.querySongs();
      allSongs.assignAll(songs);
    }
  }

  Future<void> playSong(SongModel song) async {
    try {
      currentSong.value = song;
      currentIndex.value = allSongs.indexWhere((s) => s.id == song.id);
      await audioPlayer.stop();
      await audioPlayer.setAudioSource(AudioSource.uri(Uri.parse(song.uri!)));
      await audioPlayer.seek(Duration.zero);
      await audioPlayer.play();
    } catch (e) {
      print("فشل تشغيل الأغنية: $e");
    }
  }

  Future<void> playMediaItem(MediaItem item) async {
    try {
      currentMediaItem.value = item;
      await audioPlayer.stop();
      await audioPlayer.setAudioSource(AudioSource.uri(Uri.file(item.path)));
      await audioPlayer.seek(Duration.zero);
      await audioPlayer.play();
    } catch (e) {
      print("فشل تشغيل الملف: $e");
    }
  }

  void togglePlayPause() {
    if (audioPlayer.playing) {
      audioPlayer.pause();
    } else {
      audioPlayer.play();
    }
  }

  void toggleRepeat() {
    isRepeating.value = !isRepeating.value;
  }

  void toggleShuffle() {
    isShuffleEnabled.value = !isShuffleEnabled.value;
  }

  // دالة تبديل التشغيل التلقائي للتالي
  void toggleAutoPlayNext() {
    autoPlayNext.value = !autoPlayNext.value;
  }

  // دالة تعيين وضع التكرار
  void setRepeatMode(String mode) {
    repeatMode.value = mode;
    // تحديث isRepeating بناءً على الوضع الجديد
    isRepeating.value = mode == 'one';
  }

  void replayCurrent() {
    audioPlayer.seek(Duration.zero);
    audioPlayer.play();
  }

  void seekTo(Duration position) {
    audioPlayer.seek(position);
  }

  void playNext() {
    if (currentSong.value != null && allSongs.isNotEmpty) {
      int nextIndex = (currentIndex.value + 1) % allSongs.length;
      playSong(allSongs[nextIndex]);
    } else if (currentMediaItem.value != null && playlist.isNotEmpty) {
      int nextIndex = (currentIndex.value + 1) % playlist.length;
      currentIndex.value = nextIndex;
      playMediaItem(playlist[nextIndex]);
    }
  }

  void playPrevious() {
    if (currentSong.value != null && allSongs.isNotEmpty) {
      int prevIndex = currentIndex.value - 1;
      if (prevIndex < 0) prevIndex = allSongs.length - 1;
      playSong(allSongs[prevIndex]);
    } else if (currentMediaItem.value != null && playlist.isNotEmpty) {
      int prevIndex = currentIndex.value - 1;
      if (prevIndex < 0) prevIndex = playlist.length - 1;
      currentIndex.value = prevIndex;
      playMediaItem(playlist[prevIndex]);
    }
  }

  void setPlaylist(List<dynamic> newPlaylist, int startIndex) {
    playlist.assignAll(newPlaylist);
    currentIndex.value = startIndex;
  }

  String get currentTitle {
    if (currentSong.value != null) return currentSong.value!.title;
    if (currentMediaItem.value != null) return currentMediaItem.value!.title;
    return '';
  }

  String get currentArtist {
    if (currentSong.value != null) return currentSong.value!.artist ?? 'مجهول';
    if (currentMediaItem.value != null) return 'مجهول';
    return '';
  }

  bool get hasCurrentTrack =>
      currentSong.value != null || currentMediaItem.value != null;

  // وظائف إضافية للتحكم المتقدم
  void setPlaybackSpeed(double speed) {
    playbackSpeed.value = speed;
    audioPlayer.setSpeed(speed);
  }

  void toggleLyrics() {
    showLyrics.value = !showLyrics.value;
  }

  void minimize() {
    isMinimized.value = true;
  }

  void maximize() {
    isMinimized.value = false;
  }

  // تشغيل ملف صوتي من مسار
  Future<void> playFromPath(String path,
      {String? title, String? artist}) async {
    try {
      final mediaItem = MediaItem(
        id: path.hashCode.toString(),
        title: title ?? path.split('/').last.split('.').first,
        path: path,
        duration: Duration.zero,
        type: MediaType.audio,
        size: 0,
        dateAdded: DateTime.now(),
      );

      currentMediaItem.value = mediaItem;
      await audioPlayer.stop();
      await audioPlayer.setAudioSource(AudioSource.uri(Uri.file(path)));
      await audioPlayer.seek(Duration.zero);
      await audioPlayer.play();
    } catch (e) {
      print("فشل تشغيل الملف من المسار: $e");
    }
  }

  // الحصول على صورة الألبوم
  Future<Uint8List?> getCurrentArtwork() async {
    if (currentSong.value != null) {
      return await OnAudioQuery().queryArtwork(
        currentSong.value!.id,
        ArtworkType.AUDIO,
      );
    }
    return null;
  }

  @override
  void onClose() {
    audioPlayer.dispose();
    super.onClose();
  }
}
