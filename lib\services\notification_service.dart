import 'package:get/get.dart';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:social_media_app/model/media_item.dart';

class NotificationService extends GetxService {
  Future<void> showPlayingNotification(MediaItem item) async {
    await AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: 1,
        channelKey: 'media_player',
        title: item.title,
        body: item.artist ?? 'مشغل الوسائط',
        category: NotificationCategory.Transport,
        notificationLayout: NotificationLayout.MediaPlayer,
      ),
      actionButtons: [
        NotificationActionButton(
          key: 'PREVIOUS',
          icon: 'resource://drawable/ic_previous',
          label: 'السابق',
        ),
        NotificationActionButton(
          key: 'PLAY_PAUSE',
          icon: 'resource://drawable/ic_play_pause',
          label: 'تشغيل/إيقاف',
        ),
        NotificationActionButton(
          key: 'NEXT',
          icon: 'resource://drawable/ic_next',
          label: 'التالي',
        ),
      ],
    );
  }
}
