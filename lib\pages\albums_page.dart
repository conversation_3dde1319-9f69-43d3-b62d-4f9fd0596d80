import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../ controllers/media_controller.dart';

class AlbumsPage extends StatelessWidget {
  const AlbumsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final mediaController = Get.find<MediaController>();

    return Obx(() {
      if (mediaController.albums.isEmpty) {
        return const Center(child: Text('لا توجد ألبومات'));
      }
      return GridView.builder(
        padding: const EdgeInsets.all(8),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 1.0,
          mainAxisSpacing: 8,
          crossAxisSpacing: 8,
        ),
        itemCount: mediaController.albums.length,
        itemBuilder: (ctx, index) {
          final album = mediaController.albums[index];
          return Card(
            child: InkWell(
              onTap: () {
                // عرض أغاني الألبوم
              },
              child: Column(
                children: [
                  Expanded(
                    child: Container(
                      color: Colors.grey[300],
                      child: const Icon(Icons.album, size: 60, color: Colors.blue),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      album,
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    });
  }
}