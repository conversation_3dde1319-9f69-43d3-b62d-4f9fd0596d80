import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:social_media_app/%20controllers/media_controller.dart';
import 'package:social_media_app/pages/modern_audio_player_page.dart';
import '../widgets/media_card.dart';
import '../widgets/gradient_background.dart';

class FavoritesPage extends StatelessWidget {
  const FavoritesPage({super.key});

  @override
  Widget build(BuildContext context) {
    final mediaController = Get.find<MediaController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('المفضلة'),
        backgroundColor: Colors.blue.shade800,
      ),
      body: GradientBackground(
        child: Obx(() {
          final favorites = mediaController.favoriteAudio;
          if (favorites.isEmpty) {
            return const Center(
              child: Text('لا توجد أغاني مفضلة حتى الآن'),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: favorites.length,
            itemBuilder: (context, index) {
              final item = favorites[index];
              return MediaCard(
                item: item,
                onTap: () {
                  mediaController.addToRecent(item);
                  Get.to(() => ModernAudioPlayerPage(
                        audioFile: File(item.path),
                        title: item.title,
                        artist: item.artist,
                      ));
                },
                showMoreOptions: true,
              );
            },
          );
        }),
      ),
    );
  }
}
