import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import 'package:video_player/video_player.dart';
import 'dart:io';
import 'package:file_picker/file_picker.dart';

import 'package:photo_manager/photo_manager.dart';

void main() {
  runApp(const MediaApp());
}

class MediaApp extends StatelessWidget {
  const MediaApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Lark Player Clone',
      theme: ThemeData.dark().copyWith(
        primaryColor: Colors.deepPurple,
        scaffoldBackgroundColor: Colors.black,
      ),
      home: const HomeScreen(),
    );
  }
}

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Lark Player'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {},
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {},
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(12),
        children: [
          const Text('مكتبة الوسائط', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              MediaCard(title: 'الصوتيات', icon: Icons.music_note, route: AudioScreen()),
              MediaCard(title: 'الفيديوهات', icon: Icons.video_library, route: VideoScreen()),
              MediaCard(title: 'المجلدات', icon: Icons.folder, route: FolderScreen()),
              MediaCard(title: 'المفضلة', icon: Icons.favorite, route: FavoritesScreen()),
            ],
          ),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        backgroundColor: Colors.black,
        selectedItemColor: Colors.deepPurple,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.library_music), label: 'المكتبة'),
          BottomNavigationBarItem(icon: Icon(Icons.video_collection), label: 'فيديو'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'حسابي'),
        ],
      ),
    );
  }
}

class MediaCard extends StatelessWidget {
  final String title;
  final IconData icon;
  final Widget route;

  const MediaCard({required this.title, required this.icon, required this.route, super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: Colors.grey[900],
      child: InkWell(
        onTap: () {
          Get.to(() => route);
        },
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, size: 48, color: Colors.deepPurple),
              const SizedBox(height: 8),
              Text(title, style: const TextStyle(color: Colors.white, fontSize: 16)),
            ],
          ),
        ),
      ),
    );
  }
}
///


class AudioScreen extends StatefulWidget {
  const AudioScreen({super.key});

  @override
  State<AudioScreen> createState() => _AudioScreenState();
}

class _AudioScreenState extends State<AudioScreen> {
  List<AssetEntity> audioFiles = [];

  @override
  void initState() {
    super.initState();
    _loadAllAudios();
  }

  Future<void> _loadAllAudios() async {
    print("alllllllllllllllllllll------------------");
    final permission = await PhotoManager.requestPermissionExtend();
    if (permission.isAuth) {

      List<AssetPathEntity> albums = await PhotoManager.getAssetPathList(
        type: RequestType.audio,
        onlyAll: true,
      );
      final audioAlbum = albums.first;
      final files = await audioAlbum.getAssetListPaged(page: 0, size: 1000);
      setState(() {
        audioFiles = files;
      });
    } else {
      PhotoManager.openSetting();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('كل الصوتيات')),
      body: ListView.builder(
        itemCount: audioFiles.length,
        itemBuilder: (context, index) {
          final audio = audioFiles[index];
          return FutureBuilder<File?>(
            future: audio.file,
            builder: (context, snapshot) {
              if (!snapshot.hasData) return const SizedBox();
              final file = snapshot.data!;
              return ListTile(
                leading: const Icon(Icons.music_note),
                title: Text(file.path.split('/').last),
                onTap: () {
                  Get.to(() => AudioPlayerScreen(audioFile: file));
                },
              );
            },
          );
        },
      ),
    );
  }
}

class AudioPlayerScreen extends StatefulWidget {
  final File audioFile;
  const AudioPlayerScreen({super.key, required this.audioFile});

  @override
  State<AudioPlayerScreen> createState() => _AudioPlayerScreenState();
}

class _AudioPlayerScreenState extends State<AudioPlayerScreen> {
  final AudioPlayer _player = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _player.setFilePath(widget.audioFile.path);
  }

  @override
  void dispose() {
    _player.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.audioFile.path.split('/').last)),
      body: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            StreamBuilder<PlayerState>(
              stream: _player.playerStateStream,
              builder: (context, snapshot) {
                final playing = snapshot.data?.playing ?? false;
                return IconButton(
                  icon: Icon(playing ? Icons.pause : Icons.play_arrow, size: 64),
                  onPressed: () {
                    playing ? _player.pause() : _player.play();
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

/////
//

class VideoScreen extends StatefulWidget {
  const VideoScreen({super.key});

  @override
  State<VideoScreen> createState() => _VideoScreenState();
}

class _VideoScreenState extends State<VideoScreen> {
  List<AssetEntity> videoFiles = [];

  @override
  void initState() {
    super.initState();
    _loadAllVideos();
  }

  Future<void> _loadAllVideos() async {
    final permission = await PhotoManager.requestPermissionExtend();
    if (permission.isAuth) {
      List<AssetPathEntity> albums = await PhotoManager.getAssetPathList(
        type: RequestType.video,
        onlyAll: true,
      );
      final recentAlbum = albums.first;
      final files = await recentAlbum.getAssetListPaged(page: 0, size: 1000);
      setState(() {
        videoFiles = files;
      });
    } else {
      PhotoManager.openSetting();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('جميع الفيديوهات')),
      body: ListView.builder(
        itemCount: videoFiles.length,
        itemBuilder: (context, index) {
          final video = videoFiles[index];
          return FutureBuilder<File?>(
            future: video.file,
            builder: (context, snapshot) {
              if (!snapshot.hasData) return const SizedBox();
              final file = snapshot.data!;
              return ListTile(
                leading: const Icon(Icons.video_library),
                title: Text(file.path.split('/').last),
                onTap: () {
                  Get.to(() => VideoPlayerScreen(videoFile: file));
                },
              );
            },
          );
        },
      ),
    );
  }
}

class VideoPlayerScreen extends StatefulWidget {
  final File videoFile;
  const VideoPlayerScreen({super.key, required this.videoFile});

  @override
  State<VideoPlayerScreen> createState() => _VideoPlayerScreenState();
}

class _VideoPlayerScreenState extends State<VideoPlayerScreen> {
  late VideoPlayerController _controller;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.file(widget.videoFile)
      ..initialize().then((_) {
        setState(() {});
        _controller.play();
      });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.videoFile.path.split('/').last)),
      body: Center(
        child: _controller.value.isInitialized
            ? AspectRatio(
          aspectRatio: _controller.value.aspectRatio,
          child: VideoPlayer(_controller),
        )
            : const CircularProgressIndicator(),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          setState(() {
            _controller.value.isPlaying ? _controller.pause() : _controller.play();
          });
        },
        child: Icon(
          _controller.value.isPlaying ? Icons.pause : Icons.play_arrow,
        ),
      ),
    );
  }
}
//
class FolderScreen extends StatelessWidget {
  const FolderScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('المجلدات')),
      body: ListView.builder(
        itemCount: 5,
        itemBuilder: (context, index) {
          return ListTile(
            leading: const Icon(Icons.folder),
            title: Text('مجلد ${index + 1}'),
            onTap: () {
              // عرض محتويات المجلد
            },
          );
        },
      ),
    );
  }
}

class FavoritesScreen extends StatelessWidget {
  const FavoritesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('المفضلة')),
      body: ListView.builder(
        itemCount: 3,
        itemBuilder: (context, index) {
          return ListTile(
            leading: const Icon(Icons.favorite),
            title: Text('عنصر مفضل ${index + 1}'),
            onTap: () {
              // تشغيل العنصر المفضل
            },
          );
        },
      ),
    );
  }
}
