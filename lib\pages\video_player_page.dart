import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:social_media_app/%20controllers/audio_controller.dart';
import 'package:video_player/video_player.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';

class VideoPlayerPage extends StatefulWidget {
  final File videoFile;
  const VideoPlayerPage({super.key, required this.videoFile});

  @override
  State<VideoPlayerPage> createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage> {
  VideoPlayerController? _controller;
  bool _isPlaying = false;
  bool _isLocked = false;
  bool _isLandscape = false;
  bool _isShuffleMode = false;
  bool _isLooping = false;
  int _currentVideoIndex = 0;
  List<File> _videoFiles = [];
  bool _hasError = false;
  bool _isLoading = true;
  double _volume = 1.0;
  double _brightness = 0.5;
  bool _showSidePanel = false;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  Future<void> _initializePlayer() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      await _loadVideoFiles();

      if (_videoFiles.isEmpty) {
        throw Exception("No video files found");
      }

      await _initializeController();
    } catch (e) {
      setState(() {
        _hasError = true;
        _isLoading = false;
      });
      _showErrorSnackbar("حدث خطأ أثناء تحميل الفيديو");
    }
  }

  Future<void> _loadVideoFiles() async {
    try {
      final directory = widget.videoFile.parent;
      final files = await directory.list().toList();

      _videoFiles = files
          .whereType<File>()
          .where((file) =>
              file.path.toLowerCase().endsWith('.mp4') ||
              file.path.toLowerCase().endsWith('.mov'))
          .toList();

      if (_videoFiles.isEmpty) {
        _videoFiles.add(widget.videoFile);
      }

      _currentVideoIndex =
          _videoFiles.indexWhere((file) => file.path == widget.videoFile.path);
      if (_currentVideoIndex == -1) {
        _currentVideoIndex = 0;
      }
    } catch (e) {
      throw Exception("Failed to load video files");
    }
  }

  Future<void> _initializeController() async {
    try {
      if (_currentVideoIndex < 0 || _currentVideoIndex >= _videoFiles.length) {
        throw Exception("Invalid video index");
      }

      // Pause any playing audio
      try {
        final audioController = Get.find<AudioController>();
        if (audioController.isPlaying.value) {
          audioController.togglePlayPause();
        }
      } catch (e) {
        // AudioController not found, continue
      }

      _controller = VideoPlayerController.file(
        _videoFiles[_currentVideoIndex],
        videoPlayerOptions: VideoPlayerOptions(mixWithOthers: false),
      );

      await _controller?.initialize();
      _controller?.setLooping(_isLooping);
      _controller!.addListener(_videoListener);

      if (!_controller!.value.isInitialized) {
        throw Exception("Video initialization failed");
      }

      setState(() {
        _isPlaying = true;
        _isLoading = false;
      });

      await _controller!.play();
    } catch (e) {
      if (_controller != null) {
        await _controller!.dispose();
      }
      throw Exception("Video controller initialization failed");
    }
  }

  void _videoListener() {
    if (!mounted) return;

    if (_controller!.value.position >= _controller!.value.duration &&
        !_controller!.value.isLooping) {
      _playNextVideo();
    }
    setState(() {});
  }

  Future<void> _playNextVideo() async {
    if (_videoFiles.length <= 1) return;

    int nextIndex;

    if (_isShuffleMode) {
      nextIndex = Random().nextInt(_videoFiles.length);
      while (nextIndex == _currentVideoIndex && _videoFiles.length > 1) {
        nextIndex = Random().nextInt(_videoFiles.length);
      }
    } else {
      nextIndex = (_currentVideoIndex + 1) % _videoFiles.length;
    }

    await _changeVideo(nextIndex);
  }

  Future<void> _playPreviousVideo() async {
    if (_videoFiles.length <= 1) return;

    int prevIndex;

    if (_isShuffleMode) {
      prevIndex = Random().nextInt(_videoFiles.length);
      while (prevIndex == _currentVideoIndex && _videoFiles.length > 1) {
        prevIndex = Random().nextInt(_videoFiles.length);
      }
    } else {
      prevIndex = (_currentVideoIndex - 1) % _videoFiles.length;
      if (prevIndex < 0) prevIndex = _videoFiles.length - 1;
    }

    await _changeVideo(prevIndex);
  }

  Future<void> _changeVideo(int newIndex) async {
    if (newIndex < 0 || newIndex >= _videoFiles.length) return;

    setState(() {
      _isLoading = true;
    });

    await _controller!.dispose();

    setState(() {
      _currentVideoIndex = newIndex;
    });

    await _initializeController();
  }

  Future<void> togglePlayPause() async {
    if (_isLoading || _hasError) return;

    setState(() {
      _isPlaying = !_isPlaying;
    });

    if (_isPlaying) {
      await _controller!.play();
    } else {
      await _controller!.pause();
    }
  }

  void toggleLockScreen() {
    setState(() {
      _isLocked = !_isLocked;
    });
  }

  void _toggleLooping() {
    setState(() {
      _isLooping = !_isLooping;
      _controller?.setLooping(_isLooping);
    });
  }

  Future<void> toggleOrientation() async {
    if (_isLandscape) {
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
      ]);
    } else {
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }
    setState(() {
      _isLandscape = !_isLandscape;
    });
  }

  Future<void> toggleLooping() async {
    setState(() {
      _isLooping = !_isLooping;
    });
    await _controller!.setLooping(_isLooping);
  }

  Future<void> toggleShuffleMode() async {
    setState(() {
      _isShuffleMode = !_isShuffleMode;
    });
  }

  void _showMoreOptions() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('مشاركة'),
              onTap: () {
                Get.back();
                shareVideo();
              },
            ),
            ListTile(
              leading: Icon(_isLooping ? Icons.repeat_one : Icons.repeat),
              title: Text(_isLooping ? 'إيقاف التكرار' : 'تكرار الفيديو'),
              onTap: () {
                Get.back();
                _toggleLooping();
              },
            ),
            ListTile(
              leading: Icon(_isLocked ? Icons.lock_open : Icons.lock),
              title: Text(_isLocked ? 'إلغاء القفل' : 'قفل الشاشة'),
              onTap: () {
                Get.back();
                toggleLockScreen();
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('حذف', style: TextStyle(color: Colors.red)),
              onTap: () {
                Get.back();
                deleteVideo();
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> shareVideo() async {
    try {
      await Share.shareXFiles([XFile(_videoFiles[_currentVideoIndex].path)],
          text: 'شاهد هذا الفيديو!');
    } catch (e) {
      _showErrorSnackbar("تعذر مشاركة الفيديو");
    }
  }

  Future<void> deleteVideo() async {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('حذف الفيديو'),
        content: const Text('هل تريد حذف هذا الفيديو من الجهاز؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              try {
                await _videoFiles[_currentVideoIndex].delete();
                Get.back();

                if (_videoFiles.length > 1) {
                  await _playNextVideo();
                } else {
                  Get.back();
                  Get.snackbar('تم الحذف', 'تم حذف الفيديو بنجاح',
                      snackPosition: SnackPosition.BOTTOM);
                }
              } catch (e) {
                Get.back();
                _showErrorSnackbar("حدث خطأ أثناء الحذف");
              }
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    Get.snackbar(
      'خطأ',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
  }

  String formatDuration(Duration d) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = d.inHours;
    final minutes = twoDigits(d.inMinutes.remainder(60));
    final seconds = twoDigits(d.inSeconds.remainder(60));
    return hours > 0 ? '$hours:$minutes:$seconds' : '$minutes:$seconds';
  }

  @override
  void dispose() {
    if (_controller != null && _controller!.value.isInitialized) {
      _controller!.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError) {
      return Scaffold(
        appBar: AppBar(
          title: Text(widget.videoFile.path.split('/').last),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Get.back(),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 50, color: Colors.red),
              const SizedBox(height: 20),
              const Text('حدث خطأ أثناء تحميل الفيديو',
                  style: TextStyle(fontSize: 18)),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: _initializePlayer,
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: _isLandscape
          ? null
          : AppBar(
              backgroundColor: Colors.black,
              elevation: 0,
              title: Text(
                _videoFiles.isNotEmpty
                    ? _videoFiles[_currentVideoIndex]
                        .path
                        .split('/')
                        .last
                        .split('.')
                        .first
                    : widget.videoFile.path.split('/').last.split('.').first,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              leading: IconButton(
                icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
                onPressed: () {
                  if (_isLandscape) {
                    toggleOrientation();
                  } else {
                    Get.back();
                  }
                },
              ),
              actions: [
                IconButton(
                  icon: const Icon(Icons.refresh, color: Colors.white),
                  tooltip: 'إعادة تشغيل',
                  onPressed: () => _controller?.seekTo(Duration.zero),
                ),
                IconButton(
                  icon: const Icon(Icons.more_vert, color: Colors.white),
                  onPressed: _showMoreOptions,
                ),
              ],
            ),
      body: Stack(
        children: [
          if (_isLoading)
            const Center(child: CircularProgressIndicator())
          else if (_controller != null && _controller!.value.isInitialized)
            Center(
              child: Container(
                width: double.infinity,
                height: double.infinity,
                child: FittedBox(
                  fit: BoxFit.contain,
                  child: SizedBox(
                    width: _controller!.value.size.width,
                    height: _controller!.value.size.height,
                    child: VideoPlayer(_controller!),
                  ),
                ),
              ),
            )
          else
            const Center(child: Text('فشل في تحميل الفيديو')),
          if (!_isLocked) _buildSidePanel(),
          if (!_isLocked &&
              _controller != null &&
              _controller!.value.isInitialized)
            Positioned.fill(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Main video controls with modern design
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 40),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildVideoControlButton(
                          icon: Icons.skip_previous,
                          onPressed: _videoFiles.length > 1
                              ? _playPreviousVideo
                              : null,
                        ),
                        _buildVideoControlButton(
                          icon: _isPlaying ? Icons.pause : Icons.play_arrow,
                          onPressed: togglePlayPause,
                          isMain: true,
                        ),
                        _buildVideoControlButton(
                          icon: Icons.skip_next,
                          onPressed:
                              _videoFiles.length > 1 ? _playNextVideo : null,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          if (!_isLocked &&
              _controller != null &&
              _controller!.value.isInitialized)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.7),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
                child: Column(
                  children: [
                    // Progress bar
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: VideoProgressIndicator(
                        _controller!,
                        allowScrubbing: true,
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        colors: const VideoProgressColors(
                          playedColor: Colors.white,
                          bufferedColor: Colors.white24,
                          backgroundColor: Colors.white12,
                        ),
                      ),
                    ),
                    // Time indicators
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Row(
                        children: [
                          Text(
                            formatDuration(_controller!.value.position),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            formatDuration(_controller!.value.duration),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 10),
                    // Bottom control buttons
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 40, vertical: 15),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildBottomControlButton(
                            icon: Icons.headphones,
                            onPressed: () {},
                          ),
                          _buildBottomControlButton(
                            icon: Icons.repeat,
                            onPressed: _toggleLooping,
                            isActive: _isLooping,
                          ),
                          _buildBottomControlButton(
                            icon: Icons.lock,
                            onPressed: toggleLockScreen,
                            isActive: _isLocked,
                          ),
                          _buildBottomControlButton(
                            icon: Icons.fullscreen,
                            onPressed: toggleOrientation,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSidePanel() {
    return Positioned(
      right: 0,
      top: 0,
      bottom: 0,
      child: GestureDetector(
        onTap: () => setState(() => _showSidePanel = !_showSidePanel),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          width: _showSidePanel ? 150 : 30,
          color: Colors.black54,
          child:
              _showSidePanel ? _buildExpandedPanel() : _buildCollapsedPanel(),
        ),
      ),
    );
  }

  Widget _buildExpandedPanel() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildSliderControl(
          icon: Icons.volume_up,
          value: _volume,
          onChanged: (v) => setState(() => _volume = v),
        ),
        _buildSliderControl(
          icon: Icons.brightness_6,
          value: _brightness,
          onChanged: (v) => setState(() => _brightness = v),
        ),
        IconButton(
          icon: const Icon(Icons.list, color: Colors.white),
          onPressed: () {
            Get.bottomSheet(
              Container(
                height: Get.height * 0.7,
                color: Colors.black87,
                child: ListView.builder(
                  itemCount: _videoFiles.length,
                  itemBuilder: (ctx, index) => ListTile(
                    title: Text(
                      _videoFiles[index].path.split('/').last,
                      style: TextStyle(
                        color: index == _currentVideoIndex
                            ? Colors.blue
                            : Colors.white,
                      ),
                    ),
                    onTap: () => _changeVideo(index),
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildCollapsedPanel() {
    return const Center(
      child: RotatedBox(
        quarterTurns: 1,
        child: Text(
          'التحكم',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildSliderControl({
    required IconData icon,
    required double value,
    required Function(double) onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        children: [
          Icon(icon, color: Colors.white),
          SizedBox(
            height: 100,
            child: RotatedBox(
              quarterTurns: 3,
              child: Slider(
                value: value,
                onChanged: onChanged,
                activeColor: Colors.blue,
                inactiveColor: Colors.grey,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoControlButton({
    required IconData icon,
    required VoidCallback? onPressed,
    bool isMain = false,
  }) {
    return Container(
      width: isMain ? 60 : 50,
      height: isMain ? 60 : 50,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white.withValues(alpha: isMain ? 0.9 : 0.7),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(30),
          onTap: onPressed,
          child: Icon(
            icon,
            color: onPressed != null ? Colors.black : Colors.grey,
            size: isMain ? 30 : 24,
          ),
        ),
      ),
    );
  }

  Widget _buildBottomControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    bool isActive = false,
  }) {
    return Container(
      width: 45,
      height: 45,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isActive
            ? Colors.white.withValues(alpha: 0.2)
            : Colors.white.withValues(alpha: 0.1),
        border: Border.all(
          color: isActive
              ? Colors.white.withValues(alpha: 0.3)
              : Colors.white.withValues(alpha: 0.2),
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(22.5),
          onTap: onPressed,
          child: Icon(
            icon,
            color:
                isActive ? Colors.white : Colors.white.withValues(alpha: 0.8),
            size: 20,
          ),
        ),
      ),
    );
  }
}
