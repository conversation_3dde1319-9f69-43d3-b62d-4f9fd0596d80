import 'package:get/get.dart';

abstract class BaseController extends GetxController {
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  
  void setLoading(bool loading) => isLoading.value = loading;
  void setError(String error) => errorMessage.value = error;
  void clearError() => errorMessage.value = '';
  
  @override
  void onInit() {
    super.onInit();
    initializeController();
  }
  
  void initializeController();
}